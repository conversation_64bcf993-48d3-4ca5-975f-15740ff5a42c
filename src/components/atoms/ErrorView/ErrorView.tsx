import React, { useCallback } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useRouter } from 'expo-router';
import dark from 'core/constants/themes/dark';
import _isNil from 'lodash/isNil';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import Header from 'shared/Header';
import SecondaryButton from '../SecondaryButton';
import TertiaryButton from '../TertiaryButton';
import useGoBack from 'navigator/hooks/useGoBack';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: dark.colors.background,
  },
  error: {
    color: 'white',
    textAlign: 'center',
  },
});

const ErrorView = (props: any) => {
  const { errorMessage, onRetry, actionConfig, isHeaderVisible = true } = props;
  const router = useRouter();

  const goToHome = useCallback(() => router.replace('/home'), [router]);

  const renderRetry = () => {
    if (!onRetry) return null;

    return (
      <View style={{ marginTop: 12 }}>
        <SecondaryButton label="Retry" onPress={onRetry} />
      </View>
    );
  };

  const renderGoHomeButton = () => (
    <View style={{ marginTop: 12 }}>
      <TertiaryButton title="Go Home" onPress={goToHome} />
    </View>
  );

  const renderAction = () => {
    if (_isNil(actionConfig)) {
      return null;
    }
    const { label, onPress } = actionConfig;
    return (
      <View style={{ marginTop: 12 }}>
        <TertiaryButton title={label} onPress={onPress} />
      </View>
    );
  };

  const { goBack } = useGoBack();

  return (
    <View style={{ flex: 1, width: '100%', height: '100%' }}>
      {isHeaderVisible ? <Header /> : null}
      <View style={styles.container}>
        <View style={{ padding: 16 }}>
          <FontAwesome name="warning" size={24} color="orange" />
        </View>
        <Text style={styles.error}>{errorMessage}</Text>
        {renderRetry()}
        {renderAction()}
        {renderGoHomeButton()}
      </View>
    </View>
  );
};

export default React.memo(ErrorView);
