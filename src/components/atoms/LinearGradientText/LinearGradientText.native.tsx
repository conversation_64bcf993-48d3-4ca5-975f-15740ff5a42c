import React from 'react';
import { Style<PERSON>rop, Text, TextStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';
import Dark from 'core/constants/themes/dark';

interface GradientTextProps {
  colors?: string[];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  textStyle?: StyleProp<TextStyle>;
  children: React.ReactNode;
  numberOfLines?: number;
}

const GradientText: React.FC<GradientTextProps> = (props) => {
  const {
    colors = [Dark.colors.gradientLeft, Dark.colors.gradientRight],
    start,
    end,
    textStyle,
    children,
    numberOfLines,
  } = props;
  return (
    <MaskedView maskElement={<Text {...props} />}>
      <LinearGradient
        colors={colors}
        start={start || { x: 0, y: 0 }}
        end={end || { x: 1, y: 0 }}
      >
        <Text
          {...props}
          style={[{ fontFamily: 'Montserrat-700' }, textStyle, { opacity: 0 }]}
          numberOfLines={numberOfLines}
        >
          {children}
        </Text>
      </LinearGradient>
    </MaskedView>
  );
};

export default React.memo(GradientText);
