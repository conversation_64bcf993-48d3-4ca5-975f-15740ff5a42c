import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import Icon, { ICON_TYPES } from '../index';

describe('Icon', () => {
  it('renders without crashing', async () => {
    render(
      <Icon
        type={ICON_TYPES.FONT_AWESOME as keyof typeof ICON_TYPES}
        name="rocket"
        size={30}
        color="white"
      />,
    );
    await waitFor(() => {
      expect(true).toBe(true);
    });
  });
});
