import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  dropdown: {
    height: 40,
    borderColor: dark.colors.tertiary,
    backgroundColor: dark.colors.primary,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  icon: {
    marginRight: 5,
  },
  label: {
    paddingHorizontal: 8,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
  placeholderStyle: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
  selectedTextStyle: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: 'white',
  },
  iconStyle: {
    width: 20,
    color: dark.colors.textDark,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    color: dark.colors.textDark,
    fontSize: 14,
    borderRadius: 12,
    fontFamily: 'Montserrat-500',
  },
});

export default styles;
