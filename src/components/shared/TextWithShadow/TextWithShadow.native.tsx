import React from 'react';
import { Text, View } from 'react-native';
import styles from './TextWithShadow.style';
import { StrokeText } from '@charmy.tech/react-native-stroke-text';

const TextWithShadow = ({
  text = "Text",
  shadowColor = 'black',
  shadowWidth = 2,
  shadowOffsetX = 2,
  shadowOffsetY = 2,
  textStyle = {},
  containerStyle = {},
  adjustsFontSizeToFit = false,
  numberOfLines = 1,
  strokeColor = "transparent",  
  strokeWidth = 0,          
  ...otherProps
}) => {

return (
    <View style={[styles.container, containerStyle]}>
      {shadowWidth > 0 && (
        <Text
          style={[
            styles.shadowText,
            {
              right: shadowOffsetX,
              bottom: shadowOffsetY,
              ...textStyle,
              color: shadowColor,
            },
          ]}
          adjustsFontSizeToFit={adjustsFontSizeToFit}
          numberOfLines={numberOfLines}
          {...otherProps}
        >
          {text}
        </Text>
      )}
      <StrokeText
        text={String(text)}
        color={textStyle?.color}
        strokeColor={strokeColor}
        strokeWidth={strokeWidth}
        fontSize={textStyle?.fontSize}
        fontFamily={textStyle?.fontFamily}
        {...otherProps}
      />
    </View>
  );
};

export default React.memo(TextWithShadow);
