import React, { useCallback } from 'react';
import { ListItem, VirtualizedListProps } from './types';
import { FlashList } from '@shopify/flash-list';

const VirtualizedListNative: React.FC<VirtualizedListProps> = ({
  data,
  renderItem,
  loadMore,
  hasMore,
  keyExtractor,
  ListFooterComponent,
  inverted,
  contentContainerStyle,
}) => {
  const onEndReached = useCallback(() => {
    if (hasMore) {
      loadMore?.();
    }
  }, [hasMore, loadMore]);

  const _renderItem: any = useCallback(
    ({ item }: { item: any }) => renderItem(item),
    [renderItem],
  );

  return (
    <FlashList
      data={data}
      renderItem={_renderItem}
      keyExtractor={keyExtractor}
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      ListFooterComponent={ListFooterComponent as any}
      inverted={true}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={contentContainerStyle}
    />
  );
};

export default VirtualizedListNative;
