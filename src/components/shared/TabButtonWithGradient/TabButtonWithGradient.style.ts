import { StyleSheet } from 'react-native';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from '@/src/core/constants/themes/dark';

const getTabButtonStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    tabHeaderContainer: {
      flexDirection: 'row',
      gap: 12,
      padding: 8,
      borderRadius: 12,
    },
    gradientContainer: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      bottom: isCompactMode ? -10 : -90,
      zIndex: 0,
      overflow: 'hidden',
    },
    gradientBackground: {
      position: 'absolute',
      top: 0,
      bottom: 0,
    },
    contentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      zIndex: 1,
    },
    tabButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'left',
      justifyContent: 'left',
      paddingVertical: 14,
      paddingHorizontal: 16,
      borderRadius: 8,
      gap: 8,
      borderWidth: 1,
      borderColor: '#3A3A3A',
    },
    activeTabButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: 'rgba(76, 175, 80, 0.2)', // more subtle border
      shadowColor: '#4CAF50',
      // shadowOffset: {
      //   width: 0,
      //   height: 0,
      // },
      // shadowOpacity: 0.2,
      //shadowRadius: 12,
      borderRadius: 8,
      overflow: 'hidden',
      position: 'relative',
    },

    tabButtonText: {
      fontSize: 16,
      color: dark.colors.textDark,
      fontFamily: 'Montserrat-600',
    },
    activeTabText: {
      color: '#FCFCFC',
    },
    tabContent: {
      width: '100%',
    },
    tabContentContainer: {
      flexGrow: 1,
      width: '100%',
      height: '100%',
    },
  });

const useTabButtonStyles = () => {
  const { isMobile } = useMediaQuery();
  return useMemo(() => getTabButtonStyles(isMobile), [isMobile]);
};

export default useTabButtonStyles;
