import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import GameStartTimer from 'modules/game/components/GameStartTimer';
import styles from './useGameWaitingTimer.style';

const useGameWaitingTimer = ({ game }: any) => {
  const { startTime } = game;
  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTime();
  const timeDiff = startTimeDate.getTime() - currentTime;

  const [isReady, setIsReady] = useState(false);

  const prevTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (prevTimeoutRef.current) {
      clearTimeout(prevTimeoutRef.current);
    }
    prevTimeoutRef.current = setTimeout(
      () => setIsReady(true),
      Math.max(0, timeDiff),
    );
    return () => {
      clearTimeout(prevTimeoutRef.current);
    };
  }, [timeDiff]);

  const renderQuestionOverlay = useCallback(() => {
    if (timeDiff <= 0 || isReady) {
      return null;
    }

    return (
      <View style={styles.timerOverlayContainer}>
        <GameStartTimer time={Math.max(timeDiff, 0)} />
      </View>
    );
  }, [timeDiff, isReady]);

  return {
    isReady,
    timeDiff,
    renderQuestionOverlay,
  };
};

export default useGameWaitingTimer;
