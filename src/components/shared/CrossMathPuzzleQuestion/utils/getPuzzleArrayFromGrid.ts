import _get from 'lodash/get';
import uuid from 'react-native-uuid';
import { cellType } from '../types/crossMathCellType';

const VALID_GRID_CELL_KEYS = [
  'id',
  'footerId',
  'value',
  'type',
  'isVisible',
  'pos',
  'editable',
];

const getPuzzleArrayFromGrid = (cells: any): cellType[] => {
  const result = [] as cellType[];
  let position = 0;

  for (let row = 0; row < cells.length; row++) {
    for (let col = 0; col < cells[row].length; col++) {
      const cell = cells[row][col];
      const isVisible = _get(cell, 'isVisible', true);
      result.push({
        footerId: null,
        isVisible,
        type: _get(cell, 'type'),
        id: uuid.v4().toString(),
        pos: position,
        value: isVisible ? cell.value : '',
        editable: !isVisible,
      });
      position++;
    }
  }

  return result;
};

export default getPuzzleArrayFromGrid;
