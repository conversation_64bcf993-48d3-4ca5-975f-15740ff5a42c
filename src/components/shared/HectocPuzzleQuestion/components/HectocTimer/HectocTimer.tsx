import React, { useEffect, useRef, useState } from 'react';
import { Text, View } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from 'core/constants/themes/dark';
import _isNaN from 'lodash/isNaN';
import _toString from 'lodash/toString';
import { useHectocPuzzleQuestion } from '../../context'; 
import styles from './HectocTimer.style';

const HectocTimer: React.FC = () => {
  const { state } = useHectocPuzzleQuestion(); 
  const { startTime } = state; 
  const [elapsedTime, setElapsedTime] = useState(0);
  const intervalRef = useRef(null); 

  useEffect(() => {
    const tick = () => {
      const now = Date.now();
      const start = !_isNaN(startTime) && startTime > 0 ? startTime : now;
      const elapsed = Math.floor((now - start) / 1000);
      setElapsedTime(elapsed >= 0 ? elapsed : 0);
    };

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (!_isNaN(startTime) && startTime > 0) {
      tick(); 
      intervalRef.current = setInterval(tick, 1000);
    } else {
      setElapsedTime(0);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [startTime]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${_toString(minutes).padStart(2, '0')}:${_toString(secs).padStart(
      2,
      '0',
    )}`;
  };

  return (
    <View style={styles.container}>
      <MaterialIcons
        name="timer"
        color={dark.colors.puzzle.primary}
        size={16}
      />
      <Text style={styles.time}>{formatTime(elapsedTime)}</Text>
    </View>
  );
};

export default React.memo(HectocTimer);
