import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    height: 36,
    backgroundColor: dark.colors.background,
    borderColor: dark.colors.puzzle.primary,
    borderWidth: 0.5,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
    flexDirection: 'row',
    marginTop: 12,
  },
  time: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 1,
    minWidth: 47,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Montserrat-700',
    color: dark.colors.puzzle.primary,
  },
});

export default styles;
