import { createContext, useContext } from 'react';
import { HectocContextValue } from './types';

export const HectocContext = createContext<HectocContextValue | undefined>(
  undefined,
);

export const useHectocPuzzleQuestion = () => {
  const context = useContext(HectocContext);
  if (!context) {
    throw new Error(
      'HectocPuzzleQuestion components must be used within a HectocPuzzleQuestion provider',
    );
  }
  return context;
};
