import { KeyboardType } from "../CustomKeyboard/types";

export interface FractionInputProps {
  question: {
    id?: string;
    expression: string[];
    answers: string[];
  };
  onSubmit: ({
    questionId,
    value,
  }: {
    questionId: string;
    value: string;
  }) => void;
  onChangeText: (text: string) => void;
  error?: string;
  customKeyboardType?: KeyboardType;
  customKeyboard?: boolean;
  value: string;
  answer: string;
}