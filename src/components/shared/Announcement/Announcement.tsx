import React, { useEffect } from 'react';
import { Image, Text, View } from 'react-native';
import { Announcement, AnnouncementType } from 'core/types/announcement';
import Rive from 'atoms/Rive';
import _map from 'lodash/map';
import announcementReader from 'core/readers/announcementReader';
import _reverse from 'lodash/reverse';
import styles from './Announcement.style';
import { formatDate } from './util';
import AnnouncementCTA from './components/AnnouncementCTA';

interface AnnouncementProps {
  announcement: Announcement;
  onMarkAsRead?: (id: string) => void;
  closeAnnouncement: () => void;
}

const typeColors: Record<AnnouncementType, string> = {
  [AnnouncementType.GENERAL]: '#6B7280',
  [AnnouncementType.FEATURE_UPDATE]: '#3B82F6',
  [AnnouncementType.PROMOTION]: '#10B981',
  [AnnouncementType.EVENT]: '#F59E0B',
  [AnnouncementType.MAINTENANCE]: '#EF4444',
  [AnnouncementType.SURVEY]: '#8B5CF6',
};

const typeText: Record<AnnouncementType, string> = {
  [AnnouncementType.GENERAL]: 'General',
  [AnnouncementType.FEATURE_UPDATE]: 'New Feature',
  [AnnouncementType.PROMOTION]: 'Promotion',
  [AnnouncementType.EVENT]: 'Event',
  [AnnouncementType.MAINTENANCE]: 'Maintenance',
  [AnnouncementType.SURVEY]: 'Survey',
};

interface AnnouncementTypeBadgeProps {
  type: AnnouncementType;
}

const AnnouncementTypeBadge: React.FC<AnnouncementTypeBadgeProps> = ({
  type,
}) => (
  <View
    style={[styles.badge, { backgroundColor: typeColors[type] || '#6B7280' }]}
  >
    <Text style={styles.badgeText}>{typeText[type] || 'Unknown'}</Text>
  </View>
);

const AnnouncementComponent: React.FC<AnnouncementProps> = ({
  announcement,
  onMarkAsRead,
  closeAnnouncement,
}) => {
  useEffect(() => {
    onMarkAsRead(announcementReader.id(announcement));
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <AnnouncementTypeBadge type={announcement.type} />
        {announcement.publishedAt && (
          <Text style={styles.date}>
            Published: {formatDate(announcement.publishedAt)}
          </Text>
        )}
      </View>

      {announcement.title && (
        <Text style={styles.title}>{announcement.title}</Text>
      )}

      <Text style={styles.description}>{announcement.description}</Text>

      {announcement.riveAnimationUrl && (
        <Rive
          url={announcement.riveAnimationUrl}
          autoPlay
          style={styles.image}
        />
      )}
      {announcement.imageUrl && (
        <Image
          source={{ uri: announcement.imageUrl }}
          style={styles.image}
          resizeMode="cover"
        />
      )}

      {announcement.ctas && announcement.ctas.length > 0 && (
        <View style={styles.ctaContainer}>
          {_map(
            _reverse(
              announcementReader.getSortedAnnouncementCTAsByPriority(
                announcement,
              ),
            ),
            (cta, index) => (
              <AnnouncementCTA
                key={index}
                cta={cta}
                announcementId={announcement.id}
                closeAnnouncement={closeAnnouncement}
              />
            ),
          )}
        </View>
      )}
    </View>
  );
};

export default React.memo(AnnouncementComponent);
