import { useCallback, useEffect } from 'react';
import { openBottomSheet } from 'molecules/BottomSheet/BottomSheet';
import dark from 'core/constants/themes/dark';
import { Announcement as AnnouncementType } from 'core/types/announcement';
import announcementReader from 'core/readers/announcementReader';
import Announcement from './Announcement';
import useAnnouncement from './hooks/useAnnouncement';

/**
 * Hook to handle displaying unread announcements in a bottom sheet
 *
 * @returns Object containing the announcements and loading state
 */
const announcementShown = {};

const AnnouncementHandler = () => {
  const { announcements, loading, error, markAsRead } = useAnnouncement({
    limit: 1,
    offset: 0,
    autoMarkAsRead: false,
  });

  // Function to show an announcement in a bottom sheet
  const showAnnouncementInBottomSheet = useCallback(
    (announcement: AnnouncementType) => {
      if (announcementShown[announcementReader.id(announcement)]) {
        return;
      }
      announcementShown[announcementReader.id(announcement)] = true;
      openBottomSheet({
        content: ({ closeBottomSheet }) => (
          <Announcement
            announcement={announcement}
            onMarkAsRead={(id) => {
              markAsRead(id);
            }}
            closeAnnouncement={closeBottomSheet}
          />
        ),
        styles: {
          frame: {
            borderTopColor: dark.colors.secondary,
          },
        },
      });
    },
    [markAsRead],
  );

  // Check for unread announcements when the component mounts or when announcements change
  useEffect(() => {
    if (!loading && announcements.length > 0 && !error) {
      const latestAnnouncement = announcements[0];
      showAnnouncementInBottomSheet(latestAnnouncement);
    }
  }, [announcements, loading, showAnnouncementInBottomSheet, error]);

  return null;
};

export default AnnouncementHandler;
