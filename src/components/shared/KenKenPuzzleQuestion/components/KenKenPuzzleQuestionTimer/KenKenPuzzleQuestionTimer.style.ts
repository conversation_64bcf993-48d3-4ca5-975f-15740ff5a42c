import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    height: 36,
    backgroundColor: dark.colors.background,
    borderColor: dark.colors.puzzle.primary,
    borderWidth: 0.5,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
    flexDirection: 'row',
    alignSelf: 'center',
    marginVertical: 10,
  },
  time: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Montserrat-600',
    color: dark.colors.puzzle.primary,
    fontVariant: ['tabular-nums'],
  },
  checkButton: {
    width: 30,
    height: 30,
    padding: 0,
    minWidth: 36,
    marginLeft: 8,
  },
});

export default styles;
