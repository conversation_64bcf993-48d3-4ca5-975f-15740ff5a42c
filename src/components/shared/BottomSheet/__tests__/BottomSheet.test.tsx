import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Text } from 'react-native';
import BottomSheet from '../BottomSheet';
import { BottomSheetProps } from '../types';

describe('BottomSheet', () => {
  const mockOnClose = jest.fn();
  const childText = 'BottomSheet Content';

  const defaultProps: BottomSheetProps = {
    isOpen: true,
    onClose: mockOnClose,
    children: <Text>{childText}</Text>,
  };

  beforeEach(() => {
    mockOnClose.mockClear();
  });

  it('renders children when isOpen is true', () => {
    const { getByText } = render(<BottomSheet {...defaultProps} />);
    expect(getByText(childText)).toBeTruthy();
  });

  it('does not render children when isOpen is false', () => {
    const { queryByText } = render(
      <BottomSheet {...defaultProps} isOpen={false} />,
    );
    expect(queryByText(childText)).toBeNull();
  });

  it('calls onClose when the close button is pressed', () => {
    const { getByTestId } = render(<BottomSheet {...defaultProps} />);
    const closeButton = getByTestId('bottom-sheet-close-button');
    fireEvent.press(closeButton);
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('matches snapshot when open', () => {
    const { toJSON } = render(<BottomSheet {...defaultProps} />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('matches snapshot when closed', () => {
    const { toJSON } = render(<BottomSheet {...defaultProps} isOpen={false} />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('renders with custom snap points', () => {
    const snapPoints = [50, 80];
    const { toJSON } = render(
      <BottomSheet {...defaultProps} snapPoints={snapPoints} />,
    );
    expect(toJSON()).toMatchSnapshot();
  });

  it('renders with custom styles', () => {
    const customStyles = {
      closeButton: { backgroundColor: 'green' },
      frame: { padding: 20 },
      contentContainer: { marginTop: 10 },
    };
    const { toJSON } = render(
      <BottomSheet {...defaultProps} styles={customStyles} />,
    );
    expect(toJSON()).toMatchSnapshot();
  });
});
