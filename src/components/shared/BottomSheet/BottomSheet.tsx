import React from 'react';
import { Dimensions, View } from 'react-native';
import { Sheet, YStack } from 'tamagui';
import { AntDesign } from '@expo/vector-icons';
import dark from '@/src/core/constants/themes/dark';
import { BottomSheetProps } from './types';

const { width } = Dimensions.get('window');

const BottomSheet: React.FC<BottomSheetProps> = ({
  children,
  isOpen,
  onClose,
  snapPoints = [70],
  styles = {},
  position,
  onPositionChange,
}) => (
  <Sheet
    forceRemoveScrollEnabled={isOpen}
    modal
    open={isOpen}
    snapPoints={snapPoints}
    position={position}
    onPositionChange={onPositionChange}
    dismissOnSnapToBottom
    animation="medium"
    onOpenChange={(isOpen: boolean) => {
      if (!isOpen) {
        onClose();
      }
    }}
  >
    <Sheet.Overlay
      animation="lazy"
      enterStyle={{ opacity: 0 }}
      exitStyle={{ opacity: 0 }}
    />
    <Sheet.Handle />
    <View
      style={[
        {
          position: 'absolute',
          width: 72,
          height: 44,
          backgroundColor: dark.colors.primary,
          opacity: 0.4,
          borderWidth: 1,
          borderColor: dark.colors.tertiary,
          top: -45,
          left: width / 2.5,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 25,
        },
        styles.closeButton,
      ]}
    >
      <AntDesign
        name="close"
        color={dark.colors.textDark}
        size={22}
        onPress={onClose}
        testID="bottom-sheet-close-button"
      />
    </View>

    <Sheet.Frame
      padding="$2"
      backgroundColor={dark.colors.background}
      borderTopLeftRadius={50}
      borderTopRightRadius={50}
      borderTopColor={dark.colors.secondary}
      borderTopWidth={4}
      {...styles.frame}
    >
      <Sheet.ScrollView showsVerticalScrollIndicator={false}>
        <YStack space="$4">
          <YStack {...styles.contentContainer}>{children}</YStack>
        </YStack>
      </Sheet.ScrollView>
    </Sheet.Frame>
  </Sheet>
);

export default BottomSheet;
