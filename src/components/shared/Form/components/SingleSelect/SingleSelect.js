import React from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import { FORM_INPUT_TYPES } from 'core/constants/forms';
import _values from 'lodash/values';
import styles from './SingleSelect.style';
import FormFieldLabel from '../FormFieldLabel';
import FormFieldError from '../FormFieldError';

const SingleSelect = (props) => {
  const { field, value, error, onValueChange } = props;
  const { type, options } = field;

  if (type !== FORM_INPUT_TYPES.SINGLE_SELECT) {
    return null;
  }

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.option, item === value && styles.selectedOption]}
      onPress={() => onValueChange(item)}
    >
      <Text style={styles.optionText}>{item}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.mainContainer}>
      <FormFieldLabel field={field} />
      <FlatList
        data={options}
        renderItem={renderItem}
        keyExtractor={(item) => item}
        extraData={value}
      />
      <FormFieldError error={error} />
    </View>
  );
};

SingleSelect.propTypes = {
  field: PropTypes.shape({
    type: PropTypes.oneOf(_values(FORM_INPUT_TYPES)),
    label: PropTypes.string,
    options: PropTypes.arrayOf(PropTypes.string).isRequired,
  }).isRequired,
  value: PropTypes.string.isRequired,
  onValueChange: PropTypes.func.isRequired,
  error: PropTypes.string,
};

export default React.memo(SingleSelect);
