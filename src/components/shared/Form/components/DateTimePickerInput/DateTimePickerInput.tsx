import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Platform, View } from 'react-native';
import { Button, Text, Theme, XStack } from 'tamagui';
import Entypo from '@expo/vector-icons/Entypo';
import { closePopover, showPopover } from 'molecules/Popover/Popover';
import WebDatePicker from '@/src/components/shared/WebDatePicker';
import WebTimePicker from '@/src/components/shared/WebTimePicker';
import FormFieldLabel from '../FormFieldLabel';

let DateTimePicker = null;
if (Platform.OS !== 'web') {
  DateTimePicker = require('@react-native-community/datetimepicker').default;
}

const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const months = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];

const formatDate = (date: Date) => {
  const day = days[date.getDay()];
  const dateNum = date.getDate().toString().padStart(2, '0');
  const month = months[date.getMonth()];

  return `${day}, ${dateNum} ${month}`;
};

const formatTime = (date: Date) =>
  `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;

const DateTimePickerInput = ({
  field,
  initialDateTime = new Date(),
  onValueChange: onDateTimeChange,
  darkMode = true,
}: {
  field: any;
  initialDateTime?: Date;
  onValueChange?: (date: any) => void;
  darkMode?: boolean;
}) => {
  const [selectedDateTime, setSelectedDateTime] = useState(initialDateTime);
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  const [isTimePickerVisible, setTimePickerVisible] = useState(false);

  const onDateTimeChangeRef = useRef(onDateTimeChange);
  onDateTimeChangeRef.current = onDateTimeChange;

  useEffect(() => {
    onDateTimeChangeRef?.current?.(selectedDateTime);
  }, [selectedDateTime]);

  const showDatePicker = () => {
    setDatePickerVisible(true);
    if (Platform.OS === 'web') {
      showPopover({
        content: (
          <WebDatePicker
            currentDate={selectedDateTime}
            onDateChange={handleWebDateChange}
            onClose={hideDatePicker}
          />
        ),
        overlayLook: true,
        animationType: 'slide',
        style: {
          paddingHorizontal: 10,
          paddingVertical: 10,
        },
      });
    }
  };

  const showTimePicker = () => {
    setTimePickerVisible(true);
    if (Platform.OS === 'web') {
      showPopover({
        content: (
          <WebTimePicker
            currentTime={selectedDateTime}
            onTimeChange={handleWebTimeChange}
            onClose={hideTimePicker}
          />
        ),
        overlayLook: true,
        animationType: 'slide',
        style: {
          paddingHorizontal: 10,
          paddingVertical: 10,
        },
      });
    }
  };

  const hideDatePicker = () => {
    setDatePickerVisible(false);
    closePopover();
  };

  const hideTimePicker = () => {
    setTimePickerVisible(false);
    closePopover();
  };

  const handleDateChange = (event: any, date: any) => {
    if (Platform.OS !== 'web' && date) {
      const newDateTime = new Date(selectedDateTime);
      newDateTime.setFullYear(date.getFullYear());
      newDateTime.setMonth(date.getMonth());
      newDateTime.setDate(date.getDate());
      setSelectedDateTime(newDateTime);
    }
    hideDatePicker();
  };

  const handleTimeChange = (event: any, time: any) => {
    if (Platform.OS !== 'web' && time) {
      const newDateTime = new Date(selectedDateTime);
      newDateTime.setHours(time.getHours());
      newDateTime.setMinutes(time.getMinutes());
      setSelectedDateTime(newDateTime);
    }
    hideTimePicker();
  };

  const handleWebDateChange = useCallback(
    (date: Date) => {
      const newDateTime = new Date(selectedDateTime);
      newDateTime.setFullYear(date.getFullYear());
      newDateTime.setMonth(date.getMonth());
      newDateTime.setDate(date.getDate());
      setSelectedDateTime(newDateTime);
      hideDatePicker();
    },
    [selectedDateTime, hideDatePicker],
  );

  const handleWebTimeChange = (hours: number, minutes: number) => {
    const newDateTime = new Date(selectedDateTime);
    newDateTime.setHours(hours);
    newDateTime.setMinutes(minutes);
    setSelectedDateTime(newDateTime);
    hideTimePicker();
  };

  const renderNativePickers = () => {
    if (Platform.OS === 'web' || !DateTimePicker) return null;

    return (
      <>
        {isDatePickerVisible && (
          <DateTimePicker
            value={selectedDateTime}
            mode="date"
            display={Platform.OS === 'ios' ? 'inline' : 'default'}
            onChange={handleDateChange}
          />
        )}
        {isTimePickerVisible && (
          <DateTimePicker
            value={selectedDateTime}
            mode="time"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={handleTimeChange}
          />
        )}
      </>
    );
  };

  const renderDateTimePickerInput = () => (
    <Theme name={darkMode ? 'dark' : 'light'}>
      <XStack space={10}>
        <Button
          borderRadius={8}
          backgroundColor="$gray3"
          borderWidth={1}
          borderColor="$gray6"
          height={48}
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          onPress={showDatePicker}
          iconAfter={
            <Entypo
              name="chevron-down"
              size={24}
              color={darkMode ? 'white' : 'black'}
            />
          }
        >
          <Text fontFamily="Montserrat-500" fontSize={12}>
            {formatDate(selectedDateTime)}
          </Text>
        </Button>

        <Button
          borderRadius={8}
          backgroundColor="$gray3"
          borderWidth={1}
          borderColor="$gray6"
          height={48}
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          onPress={showTimePicker}
          iconAfter={
            <Entypo
              name="chevron-down"
              size={24}
              color={darkMode ? 'white' : 'black'}
            />
          }
        >
          <Text fontFamily="Montserrat-500" fontSize={12}>
            {formatTime(selectedDateTime)}
          </Text>
        </Button>

        {renderNativePickers()}
      </XStack>
    </Theme>
  );

  return (
    <View style={{ gap: 6, paddingVertical: 5 }}>
      <FormFieldLabel field={field} />
      {renderDateTimePickerInput()}
    </View>
  );
};

export default DateTimePickerInput;
