import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  mainContainer: {
    gap: 6,
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputContainer: {
    borderRadius: 6,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    flexDirection: 'row',
    alignItems: 'center',
  },
  subButton: {
    backgroundColor: dark.colors.primary,
    width: 26,
    height: 30,
    alignItems: 'center',
    borderTopLeftRadius: 6,
    borderBottomLeftRadius: 6,
    justifyContent: 'center',
  },
  addButton: {
    backgroundColor: dark.colors.primary,
    width: 25,
    height: 30,
    alignItems: 'center',
    borderTopRightRadius: 6,
    borderBottomRightRadius: 6,
    justifyContent: 'center',
  },
  subText: {
    color: '#FFFFFF',
    fontFamily: 'Montserrat-400',
    fontSize: 18,
  },
  addText: {
    fontFamily: 'Montserrat-400',
    color: dark.colors.secondary,
    fontSize: 18,
  },
  value: {
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: 12,
    color: 'white',
    borderRadius: 12,
    fontFamily: 'Montserrat-600',
    outlineStyle: 'none',
    borderColor: 'white',
    textAlign: 'center',
    minWidth: 35,
    width: 30,
  },
  errorText: {
    color: dark.colors.error,
  },
});

export default styles;
