import React from 'react';
import { render, act } from '@testing-library/react-native';
import DotAnimation from '../DotAnimation';

describe('DotAnimation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  it('renders correctly with initial state', () => {
    const { getByText } = render(<DotAnimation />);
    expect(getByText('.')).toBeTruthy();
  });

  it('updates dots count correctly after interval', () => {
    const { getByText, rerender } = render(<DotAnimation />);
    expect(getByText('.')).toBeTruthy();

    act(() => {
      jest.advanceTimersByTime(1200);
    });

    rerender(<DotAnimation />);
    expect(getByText('. .')).toBeTruthy();

    act(() => {
      jest.advanceTimersByTime(1200);
    });

    rerender(<DotAnimation />);
    expect(getByText('. . .')).toBeTruthy();

    act(() => {
      jest.advanceTimersByTime(1200);
    });

    rerender(<DotAnimation />);
    expect(getByText('.')).toBeTruthy();
  });

  it('cleans up interval on unmount', () => {
    const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
    const { unmount } = render(<DotAnimation />);
    unmount();
    expect(clearIntervalSpy).toHaveBeenCalled();
  });
});
