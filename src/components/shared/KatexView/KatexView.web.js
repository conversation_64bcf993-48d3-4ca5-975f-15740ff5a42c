import 'katex/dist/katex.min.css'
import { StyleSheet, View } from 'react-native'
import { useMemo } from 'react'
import _isNil from 'lodash/isNil'
import KaTeX from 'katex'

const styles = StyleSheet.create({
  container: { color: 'white', fontSize: 18 },
})

// const mathExpression = `c = \\pm\\sqrt{a^2 + b^2}`; // LaTeX expression

const KatexView = ({ expression, fontSize = 18 }) => {
  const { html, error } = useMemo(() => {
    if (_isNil(expression)) return null
    try {
      const html = KaTeX.renderToString(expression, {
        displayMode: false,
        throwOnError: false,
      })

      return { html, error: undefined }
    } catch (error) {
      if (error instanceof KaTeX.ParseError || error instanceof TypeError) {
        return { error }
      }

      throw error
    }
  }, [expression])

  if (_isNil(expression)) {
    return null
  }

  return (
    <View style={[styles.container, { fontSize }]}>
      <div
        style={styles.mathContainer}
        dangerouslySetInnerHTML={{ __html: html }}
      />
    </View>
  )
}

export default KatexView
