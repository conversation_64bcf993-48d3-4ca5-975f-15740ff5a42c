import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  card: {
    width: '100%',
    flexDirection: 'column',
    alignItems: 'flex-start',
    backgroundColor: dark.colors.cardBackground,
    borderRadius: 10,
    padding: 15,
  },
  iconContainer: {
    backgroundColor: '#343434',
    padding: 8,
    borderRadius: 8,
  },
  textContainer: {
    // flex: 1,
  },
  iconStyle: {
    width: 24,
    height: 24,
  },
  title: {
    lineHeight: 17,
    color: 'white',
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  subtitle: {
    marginTop: 6,
    lineHeight: 14,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    fontSize: 12,
  },
  footerText: {
    textAlign: 'center',
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    fontSize: 8,
    marginTop: 0,
  },
  footer: {
    backgroundColor: dark.colors.tertiary,
    marginTop: 5,
    marginLeft: 50,
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 15.5,
  },
  arrowContainer: {
    alignItems: 'center',
    paddingLeft: 10,
  },
});

export default styles;
