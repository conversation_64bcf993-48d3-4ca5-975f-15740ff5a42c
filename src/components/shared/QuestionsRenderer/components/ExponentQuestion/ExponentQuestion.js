import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import KatexView from '../../../KatexView';
import dark from '../../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  tagContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
    backgroundColor: dark.colors.gradientBackground,
    flexShrink: 1,
  },
  tagText: {
    color: dark.colors.textDark,
  },
});

const ExponentQuestion = (props) => {
  const { question } = props;

  const { isMobile: isCompactMode } = useMediaQuery();

  const { base, exponent } = question;

  const fontSize = Platform.OS !== 'web' ? 64 : isCompactMode ? 28 : 40;

  return (
    <View style={{ width: '100%' }}>
      <View
        style={{
          width: '100%',
          flexDirection: 'row',
          justifyContent: 'center',
        }}
      >
        <KatexView expression={`${base}^{${exponent}}`} fontSize={fontSize} />
      </View>
      {/* <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 16}}> */}
      {/* <View style={styles.tagContainer}>
                    <Text style={styles.tagText}>{tag}</Text>
                </View> */}
      {/* </View> */}
    </View>
  );
};

export default React.memo(ExponentQuestion);
