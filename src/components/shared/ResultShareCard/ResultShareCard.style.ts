import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  imageBackground: {
    height: 323,
    width: 290,
    overflow: 'hidden',
  },
  innerContainer: {
    paddingHorizontal: 18,
    paddingVertical: 18,
    gap: 0,
    width: '100%',
  },
  matiksText: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    color: dark.colors.textDarkWithLowOpacity,
    textAlign: 'center',
    letterSpacing: 1,
  },
  withFlexOneAndCenter: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userImageStyle: {
    height: 80,
    width: 70,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: dark.colors.textDarkWithLowOpacity,
    overflow: 'hidden',
  },
  usersInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 10,
  },
  gameTypeText: {
    fontFamily: 'Montserrat-800',
    fontSize: 20,
    letterSpacing: 2,
    color: dark.colors.secondary,
    textAlign: 'center',
  },
  vsText: {
    fontFamily: 'Montserrat-900',
    fontSize: 32,
    color: dark.colors.textDarkWithLowOpacity,
    letterSpacing: 1,
  },
  scoreText: {
    fontFamily: 'Montserrat-800',
    fontSize: 24,
    letterSpacing: 3.4,
    textAlign: 'center',
    color: 'white',
  },
  bottomContainer: {
    width: '100%',
    height: 54,
    position: 'absolute',
    alignItems: 'center',
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 18,
  },
  matiksLogo: {
    width: 12,
    height: 12,
  },
  hasWonText: {
    fontSize: 20,
    fontFamily: 'Montserrat-700',
    color: 'white',
    textAlign: 'center',
  },
});

export default styles;
