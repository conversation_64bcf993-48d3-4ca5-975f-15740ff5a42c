import { StyleSheet } from 'react-native';
import dark from '../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  contentContainer: {
    backgroundColor: dark.colors.primary,
    borderColor: dark.colors.tertiary,
    borderWidth: 2,
    borderRadius: 24,
    width: '85%',
    maxWidth: 400,
    paddingHorizontal: 20,
    paddingTop: 24,
    gap: 30,
    paddingBottom: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  compactContentContainer: {
    paddingTop: 16,
    paddingBottom: 12,
  },
  headerText: {
    fontSize: 15,
    lineHeight: 24,
    fontFamily: 'Montserrat-500',
    color: 'white',
  },
  headerRow: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    width: '100%',
  },
  footerContent: {
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    lineHeight: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
    textAlign: 'center',
    color: 'white',
  },
  title: {
    width: '100%',
    fontFamily: 'Montserrat-800',
    lineHeight: 57,
    fontSize: 36,
    color: dark.colors.secondary,
  },
  qrRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
    zIndex: -1,
  },
  SeeMoreButton: {
    marginTop: 16,
    backgroundColor: dark.colors.secondary,
    height: 32,
    width: 100,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  seeMoreText: {
    color: 'black',
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
