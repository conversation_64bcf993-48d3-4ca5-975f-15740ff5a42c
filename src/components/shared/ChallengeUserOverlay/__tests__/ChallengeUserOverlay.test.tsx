import React from 'react';
import { render } from '@testing-library/react-native';
import ChallengeUserOverlay from '../ChallengeUserOverlay';
import { CHALLENGE_USER_STATUS } from '@/src/overlays/constants/challengeUserStatus';
import USER_ACTIVITY from '@/src/core/constants/userActivityConstants';
import { ChallengeUserOverlayProps } from '../types';

jest.mock('expo-router', () => ({
  usePathname: jest.fn(),
}));

const mockUseSession =
  require('../../../../modules/auth/containers/AuthProvider').useSession;
const mockUsePathname = require('expo-router').usePathname;
const mockGetUserCurrentActivity =
  require('../../../../core/utils/getUserCurrentActivity').getUserCurrentActivity;

const DEFAULT_USER_ID = 'user123';
const OPPONENT_USER_ID = 'opponent456';

const defaultProps: ChallengeUserOverlayProps = {
  showChallengeUserOverlay: true,
  payload: {
    status: CHALLENGE_USER_STATUS.CHALLENGE_SENT,
    challengedBy: OPPONENT_USER_ID,
    opponentUser: { id: OPPONENT_USER_ID, name: 'Opponent' },
  },
};

describe('ChallengeUserOverlay', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSession.mockReturnValue({ userId: DEFAULT_USER_ID });
    mockUsePathname.mockReturnValue('/some/path');
    mockGetUserCurrentActivity.mockReturnValue(USER_ACTIVITY.EXPLORING);
  });

  it('renders null if payload is null', () => {
    const { toJSON } = render(
      <ChallengeUserOverlay {...defaultProps} payload={null} />,
    );
    expect(toJSON()).toBeNull();
  });

  it('renders ChallengeUserStatus when current user is waiting for opponent', async () => {
    const props: ChallengeUserOverlayProps = {
      ...defaultProps,
      payload: {
        ...defaultProps.payload,
        challengedBy: DEFAULT_USER_ID,
        opponentUser: { id: OPPONENT_USER_ID, name: 'Opponent' },
      },
    };
    const { getByTestId } = render(<ChallengeUserOverlay {...props} />);

    const statusElement = getByTestId('mock-challenge-status');
    const bannerElement = getByTestId('mock-banner-animation');

    expect(statusElement).toBeTruthy();
    expect(bannerElement).toBeTruthy();
  });

  it('renders ChallengeRequestCard when current user receives a challenge', async () => {
    const props: ChallengeUserOverlayProps = {
      ...defaultProps,
      payload: {
        ...defaultProps.payload,
        challengedBy: OPPONENT_USER_ID,
        opponentUser: { id: OPPONENT_USER_ID, name: 'Opponent' },
      },
    };
    const { getByTestId } = render(<ChallengeUserOverlay {...props} />);

    const requestCardElement = getByTestId('mock-challenge-request-card');
    const bannerElement = getByTestId('mock-banner-animation');

    expect(requestCardElement).toBeTruthy();
    expect(bannerElement).toBeTruthy();
  });
});
