import { Image, Text, View } from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';
import AntDesign from '@expo/vector-icons/AntDesign';
import AppDownloadQRImage from 'assets/images/matiks_qr.png';
import { Overlay } from '@rneui/base';
import styles from './AppDownloadModal.style';
import dark from '../../../core/constants/themes/dark';
import { AppDownloadModalProps } from './types';

const AppDownloadModal: React.FC<AppDownloadModalProps> = (props) => {
  const { onClose, isModalVisible } = props;
  return (
    <Overlay isVisible={isModalVisible} overlayStyle={styles.contentContainer}>
      <View style={styles.headerRow}>
        <Text style={styles.headerText}>Matiks mobile application</Text>
        <Entypo
          name="cross"
          size={22}
          color={dark.colors.textDark}
          onPress={onClose}
        />
      </View>
      <View style={styles.qrRow}>
        <Text style={styles.title}>
          Scan QR code to download Matiks mobile app !
        </Text>
        <Image
          source={AppDownloadQRImage}
          style={styles.qrImage}
          resizeMode="contain"
        />
      </View>
      <View style={styles.footerContent}>
        <AntDesign name="sound" size={15} color={dark.colors.textDark} />
        <Text style={styles.footerText}>
          Matiks is available on Play Store and App Store, Download now!.
        </Text>
      </View>
    </Overlay>
  );
};

export default AppDownloadModal;
