import dark from '@/src/core/constants/themes/dark';
import { Dimensions, StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  emptyStateContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    width: '100%',
    height: '100%',
    minHeight: Dimensions.get('window').height * 0.6,
  },
  emptyStateText: {
    marginTop: 12,
    fontSize: 16,
    fontFamily: 'Montserrat-700',
    letterSpacing: 0.5,
    color: 'white',
    marginBottom: 8,
  },
  emptyStateSubText: {
    fontSize: 14,
    lineHeight: 20,
    color: dark.colors.textDark,
    width: '80%',
    textAlign: 'center',
    fontFamily: 'Montserrat-500',
  },
  pookieImage: {
    width: 120,
    height: 100,
    marginBottom: 8,
  },
  addFriendsButton: {
    marginTop: 20,
    width: 140,
    height: 32,
    borderWidth: 1,
    borderColor: dark.colors.primary,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  addFriendsButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
  },
});

export default styles;
