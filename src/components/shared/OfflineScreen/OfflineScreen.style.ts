import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  imageContainer: {
    width: 160,
    height: 130,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: 360,
    height: 360,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    color: dark.colors.GradientCardGreen,
    fontFamily: 'Montserrat-700',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 20,
    color: '#fff',
    marginBottom: 6,
    fontFamily: 'Montserrat-500',
  },
  body: {
    textAlign: 'center',
    color: dark.colors.textDark,
    fontSize: 16,
    marginBottom: 20,
    fontFamily: 'Montserrat-500',
  },
  ipText: {
    fontSize: 13,
    color: '#666',
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#445d3c',
    paddingVertical: 12,
    paddingHorizontal: 36,
    borderRadius: 25,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  footerText: {
    textAlign: 'center',
    color: dark.colors.textDark,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    marginBottom: 16,
  },
  formButton: {
    flexShrink: 1,
    height: 14,
    marginBottom: 16,
  },
  formButtonText: {
    color: dark.colors.linkColor,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
});

export default styles;
