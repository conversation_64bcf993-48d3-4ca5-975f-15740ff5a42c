import React from 'react';
import { Defs, Path, RadialGradient, Rect, Stop, Svg } from 'react-native-svg';

const RetainedStateBackground = ({
  color = '#40D2FF',
  opacity = 0.4,
}: {
  color: string;
  opacity: number;
}) => (
  <Svg
    width="360"
    height="311"
    viewBox="0 0 360 311"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Rect
      width="258"
      height="212"
      transform="translate(51 34)"
      fill="url(#paint0_radial_318_1100)"
    />
    <Path
      d="M168.98 44.4198C175.772 40.3841 184.228 40.3841 191.02 44.4198L216.505 59.5607C217.66 60.247 218.873 60.8311 220.13 61.3063L247.857 71.7907C255.247 74.5852 260.519 81.1958 261.599 89.0227L265.65 118.387C265.834 119.719 266.134 121.031 266.546 122.31L275.636 150.525C278.059 158.045 276.178 166.288 270.732 172.013L250.299 193.489C249.373 194.462 248.534 195.515 247.791 196.635L231.4 221.333C227.031 227.917 219.413 231.585 211.542 230.896L182.012 228.312C180.673 228.195 179.327 228.195 177.988 228.312L148.458 230.896C140.587 231.585 132.969 227.917 128.6 221.333L112.209 196.635C111.466 195.515 110.627 194.462 109.701 193.489L89.2684 172.013C83.8224 166.288 81.941 158.045 84.3639 150.525L93.4544 122.31C93.8664 121.031 94.166 119.719 94.3496 118.387L98.4013 89.0227C99.4813 81.1958 104.753 74.5852 112.143 71.7907L139.87 61.3063C141.127 60.8311 142.34 60.247 143.495 59.5607L168.98 44.4198Z"
      stroke={color}
      strokeOpacity={opacity}
      strokeWidth="1.5"
    />
    <Path
      d="M168.98 8.41976C175.772 4.38411 184.228 4.3841 191.02 8.41976L229.985 31.5694C231.14 32.2557 232.353 32.8398 233.61 33.315L276.003 49.3451C283.393 52.1396 288.665 58.7502 289.745 66.577L295.939 111.474C296.123 112.805 296.423 114.118 296.835 115.397L310.733 158.535C313.156 166.056 311.275 174.299 305.829 180.023L274.589 212.86C273.663 213.833 272.824 214.886 272.081 216.005L247.019 253.768C242.65 260.351 235.033 264.02 227.162 263.331L182.012 259.38C180.673 259.263 179.327 259.263 177.988 259.38L132.838 263.331C124.967 264.02 117.35 260.351 112.981 253.768L87.9191 216.005C87.1761 214.886 86.3367 213.833 85.4106 212.86L54.171 180.023C48.725 174.299 46.8435 166.056 49.2665 158.535L63.1653 115.397C63.5774 114.118 63.8769 112.805 64.0606 111.474L70.2554 66.5771C71.3353 58.7502 76.6071 52.1396 83.9974 49.3451L126.39 33.315C127.647 32.8398 128.86 32.2557 130.015 31.5694L168.98 8.41976Z"
      stroke={color}
      strokeOpacity={opacity * 0.5}
      strokeWidth="1.5"
    />
    <Path
      d="M168.98 -42.5802C175.772 -46.6159 184.228 -46.6159 191.02 -42.5802L249.081 -8.08497C250.236 -7.39867 251.449 -6.81457 252.706 -6.33933L315.876 17.5471C323.266 20.3416 328.538 26.9522 329.618 34.7791L338.849 101.68C339.033 103.011 339.332 104.324 339.744 105.603L360.455 169.884C362.878 177.404 360.996 185.648 355.55 191.372L309 240.301C308.074 241.275 307.235 242.327 306.492 243.447L269.148 299.718C264.779 306.301 257.161 309.969 249.29 309.281L182.012 303.393C180.673 303.276 179.327 303.276 177.988 303.393L110.71 309.281C102.839 309.969 95.2214 306.301 90.8525 299.718L53.5083 243.447C52.7653 242.327 51.926 241.275 50.9998 240.301L4.44968 191.372C-0.996303 185.648 -2.87777 177.404 -0.454807 169.884L20.2558 105.603C20.6679 104.324 20.9674 103.011 21.1511 101.68L30.382 34.7791C31.4619 26.9522 36.7337 20.3416 44.124 17.5471L107.294 -6.33933C108.551 -6.81457 109.764 -7.39867 110.919 -8.08497L168.98 -42.5802Z"
      stroke={color}
      strokeOpacity={opacity * 0.175}
      strokeWidth="1.5"
    />
    <Defs>
      <RadialGradient
        id="paint0_radial_318_1100"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(134 112.5) rotate(92.8768) scale(99.6255 97.9598)"
      >
        <Stop stopColor={color} stopOpacity={opacity} />
        <Stop offset="0.92" stopColor={color} stopOpacity="0" />
      </RadialGradient>
    </Defs>
  </Svg>
);

export default RetainedStateBackground;
