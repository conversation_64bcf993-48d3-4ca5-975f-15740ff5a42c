import  React from "react"
import Svg, { <PERSON>, <PERSON>, G } from "react-native-svg"

function CancelRequestSvg(props) {
  return (
    <Svg
      width={15}
      height={15}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Mask
        id="a"
        style={{
          maskType: "alpha"
        }}
        maskUnits="userSpaceOnUse"
        x={0}
        y={0}
        width={15}
        height={15}
      >
        <Path fill="#D9D9D9" d="M0 0H14V14H0z" />
      </Mask>
      <G mask="url(#a)">
        <Path
          d="M11.667 6.417a.564.564 0 01-.415-.168.564.564 0 01-.168-.416c0-.165.056-.303.168-.415a.564.564 0 01.415-.168c.166 0 .304.056.416.168.112.112.168.25.168.415a.565.565 0 01-.168.416.564.564 0 01-.416.168zm0-1.75a.564.564 0 01-.415-.168.564.564 0 01-.168-.416v-1.75c0-.165.056-.303.168-.415a.564.564 0 01.415-.168c.166 0 .304.056.416.168.112.112.168.25.168.415v1.75a.565.565 0 01-.168.416.564.564 0 01-.416.168zM5.251 7a2.247 2.247 0 01-1.648-.685 2.247 2.247 0 01-.686-1.648c0-.642.229-1.191.686-1.648a2.247 2.247 0 011.648-.686c.641 0 1.19.229 1.648.686.457.457.685 1.006.685 1.648 0 .641-.228 1.19-.685 1.648A2.247 2.247 0 015.25 7zM.584 10.5v-.467c0-.33.085-.634.255-.911.17-.277.396-.489.678-.634a8.66 8.66 0 011.838-.679 8.026 8.026 0 013.791 0 8.66 8.66 0 011.838.679c.282.145.508.357.678.634.17.277.255.58.255.911v.467c0 .32-.114.595-.342.824a1.124 1.124 0 01-.824.343h-7c-.321 0-.596-.115-.824-.343a1.124 1.124 0 01-.343-.824zm1.167 0h7v-.467a.568.568 0 00-.292-.496 7.604 7.604 0 00-1.59-.59 6.767 6.767 0 00-3.237 0 7.604 7.604 0 00-1.59.59.568.568 0 00-.291.496v.467zm3.5-4.667c.32 0 .595-.114.824-.342.228-.229.342-.503.342-.824 0-.321-.114-.596-.342-.824A1.123 1.123 0 005.25 3.5c-.321 0-.596.114-.824.343a1.123 1.123 0 00-.343.824c0 .32.114.595.343.824.228.228.503.342.824.342z"
          fill="#BABABA"
        />
      </G>
    </Svg>
  )
}

export default React.memo(CancelRequestSvg)
