import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import BackButton from '../BackButton';
import Haptics from 'core/container/Haptics';

describe('BackButton', () => {
  it('renders without crashing', async () => {
    const { getByTestId } = render(<BackButton />);
    await waitFor(() => {
      expect(getByTestId('backButtonTest')).toBeTruthy();
    });
  });

  it('calls onBackPress when provided', async () => {
    const mockOnBackPress = jest.fn();
    const { getByTestId } = render(
      <BackButton onBackPress={mockOnBackPress} />,
    );

    fireEvent.press(getByTestId('backButtonTest'));

    await waitFor(() => {
      expect(Haptics.impactAsync).toHaveBeenCalledWith('Light');
      expect(mockOnBackPress).toHaveBeenCalled();
    });
  });

  const useGoBackHook = jest.requireActual('navigator/hooks/useGoBack');

  it('calls goBack when onBackPress is not provided', async () => {
    const mockGoBack = jest.fn();
    (useGoBackHook as any).default = () => ({ goBack: mockGoBack });

    const { getByTestId } = render(<BackButton />);

    fireEvent.press(getByTestId('backButtonTest'));

    await waitFor(() => {
      expect(Haptics.impactAsync).toHaveBeenCalledWith('Light');
      expect(mockGoBack).toHaveBeenCalled();
    });
  });
});
