import React from 'react'
import _forEach from 'lodash/forEach'
import _noop from 'lodash/noop'
import styles from './RightPane.style'
import Modal from 'react-native-modal'
import { RightPaneProps, RightPaneState } from './types'

const DEFAULT_ANIMATION_DURATION = 300

let instance: RightPane | null = null

class RightPane extends React.PureComponent<RightPaneProps, RightPaneState> {
  constructor(props: RightPaneProps) {
    super(props)
    this.state = {
      isVisible: false,
      content: null,
    }
    instance = this
  }

  static getInstance(): RightPane | null {
    return instance
  }

  componentWillUnmount() {
    instance = null
  }

  render() {
    const { isVisible, content } = this.state

    if (!isVisible && !content) {
      return null
    }

    return (
      // <Overlay onBackdropPress={this.closePane} isVisible={isVisible}>
      <Modal
        isVisible={isVisible}
        style={[
          styles.container,
          this.props.containerStyle,
          { top: 0, right: 0 },
        ]}
        animationIn="slideInRight"
        animationOut="slideOutRight"
        animationInTiming={DEFAULT_ANIMATION_DURATION}
        animationOutTiming={DEFAULT_ANIMATION_DURATION}
        backdropTransitionInTiming={DEFAULT_ANIMATION_DURATION}
        backdropTransitionOutTiming={0}
        hideModalContentWhileAnimating={true}
        useNativeDriver={true}
        onBackdropPress={this.closePane}
        statusBarTranslucent
        propagateSwipe={true}
      >
        {content}
      </Modal>
      // </Overlay>
    )
  }

  showPane = (props: Pick<RightPaneState, 'content'>) => {
    this.setState({
      ...props,
      isVisible: true,
    })
  }

  handleClose = () => {
    this.setState(
      {
        isVisible: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            content: null,
          })
        }, DEFAULT_ANIMATION_DURATION)
      }
    )
  }

  closePane = () => {
    this.handleClose()
  }
}

const defaultRightPaneProps = {
  containerStyle: {},
  content: null,
}

export function showRightPane(props: Pick<RightPaneState, 'content'>) {
  const paneInstance = RightPane.getInstance()
  if (paneInstance) {
    paneInstance.showPane({
      ...defaultRightPaneProps,
      ...props,
    })
  }
}

export function closeRightPane() {
  const paneInstance = RightPane.getInstance()
  if (paneInstance) {
    paneInstance.closePane()
  }
}

export const WrappedRightPaneComponent = React.forwardRef<RightPane, any>(({ ...restProps }, ref) => {
  return <RightPane {...restProps} ref={ref} />
})

export default WrappedRightPaneComponent;

export { WrappedRightPaneComponent as Component };
