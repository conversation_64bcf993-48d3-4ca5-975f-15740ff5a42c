import { gql } from '@apollo/client';

/**
 * GraphQL fragment for Announcement query
 */
export const ANNOUNCEMENT_FRAGMENT = gql`
  fragment AnnouncementFields on Announcement {
    id
    type
    title
    description
    imageUrl
    riveAnimationUrl
    mediaUrl
    priority
    createdAt
    publishedAt
    expiresAt
    ctas {
      text
      target
      actionType
      style
    }
  }
`;

/**
 * GraphQL query to fetch unread announcements
 */
export const GET_UNREAD_ANNOUNCEMENTS_QUERY = gql`
  query GetUnreadAnnouncements($limit: Int = 10, $offset: Int = 0) {
    getUnreadAnnouncements(limit: $limit, offset: $offset) {
      ...AnnouncementFields
    }
  }
  ${ANNOUNCEMENT_FRAGMENT}
`;

/**
 * GraphQL query to fetch a specific announcement
 */
export const GET_ANNOUNCEMENT_BY_ID_QUERY = gql`
  query GetAnnouncement($id: ID!) {
    getAnnouncement(id: $id) {
      ...AnnouncementFields
    }
  }
  ${ANNOUNCEMENT_FRAGMENT}
`;

/**
 * GraphQL mutation to mark an announcement as read
 */
export const MARK_ANNOUNCEMENT_AS_READ_MUTATION = gql`
  mutation MarkAnnouncementAsRead($announcementId: ID!) {
    markAnnouncementAsRead(announcementId: $announcementId) {
      success
      message
    }
  }
`;

/**
 * GraphQL mutation to mark all announcements as read
 */
export const MARK_ALL_ANNOUNCEMENTS_AS_READ_MUTATION = gql`
  mutation MarkAllAnnouncementsAsRead {
    markAllAnnouncementsAsRead {
      success
      message
    }
  }
`;
