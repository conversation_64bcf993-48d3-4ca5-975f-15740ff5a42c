import { gql } from '@apollo/client';

export const GLOBAL_PRESET_FRAGMENT = gql`
  fragment GlobalPresetFields on GlobalPreset {
    _id
    identifier
    globalAverageTime
    bestTime
    totalQuestionsSolved
    numOfCorrectSubmissions
    globalAccuracy
    top10Mathletes {
      userId
      questionsSolved
      bestTime
    }
  }
`;

export const USER_PRESET_FRAGMENT = gql`
  fragment UserPresetFields on UserPreset {
    _id
    globalPresetId
    userId
    identifier
    name
    questionsSolved
    curAvgTime
    curAvgAccuracy
    bestTime
    bestStreak
    numOfCorrectSubmissions
    last10Time
    last10IncorrectAttempts
    saved
    savedConfig
  }
`;

export const USER_PRESET_STATS_FRAGMENT = gql`
  fragment UserPresetStatsFields on UserPresetDayStats {
    date
    userPresetStats {
      _id
      date
      userPresetId
      globalPresetId
      identifier
      userId
      avgTime
      avgAccuracy
      bestStreak
      numOfCorrectSubmissions
      questionsSolved
      timePerformanceTrend
      inaccuracyPerformanceTrend
    }
  }
`;
