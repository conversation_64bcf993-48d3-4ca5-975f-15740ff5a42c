import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

// Fix: Ensure proper template literal syntax with backticks
const REGISTER_DEVICE_TOKEN_QUERY = gql`
  mutation RegisterDeviceToken(
    $pushNotificationToken: String!
    $deviceId: String
    $platform: String
  ) {
    registerDeviceToken(
      pushNotificationToken: $pushNotificationToken
      deviceId: $deviceId
      platform: $platform
    ) {
      success
      message
    }
  }
`;

const useRegisterDeviceToken = () => {
  // Add error and loading states
  const [registerDeviceTokenQuery, { error, loading }] = useMutation(
    REGISTER_DEVICE_TOKEN_QUERY,
    {
      // Add error handling
      onError: (error) => {
        console.error('RegisterDeviceToken mutation error:', error);
      },
    },
  );

  const registerDeviceToken = useCallback(
    ({ deviceToken, deviceId, platform } = {}) => {
      if (!deviceToken) {
        console.warn('Device token is required');
        return Promise.reject(new Error('Device token is required'));
      }

      return registerDeviceTokenQuery({
        variables: {
          pushNotificationToken: deviceToken,
          deviceId,
          platform,
        },
      }).catch((error) => {
        console.error('Failed to register device token:', error);
        throw error;
      });
    },
    [registerDeviceTokenQuery],
  );

  return {
    registerDeviceToken,
    isRegistering: loading,
    error,
  };
};

export default useRegisterDeviceToken;
