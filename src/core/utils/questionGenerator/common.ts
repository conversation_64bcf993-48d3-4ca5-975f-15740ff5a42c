import { RandomNumberOptions } from './types';

export const generateRandomNumber = (digits: number): number => {
    if (digits < 1) return 0;

    let numberStr: string = '';
    numberStr += Math.floor(Math.random() * 8) + 2;

    for (let i = 1; i < digits; i++) {
        numberStr += Math.floor(Math.random() * 10);
    }

    return parseInt(numberStr, 10);
};

export const getRandomIntInclusive = (min: number, max: number, options?: RandomNumberOptions): number => {
    min = Math.ceil(min);
    max = Math.floor(max);
    if (min > max) {
        [min, max] = [max, min];
    }
    
    const randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
    
    if (options?.excludeNumbers?.includes(randomNum)) {
        return getRandomIntInclusive(min, max, options);
    }
    
    return randomNum;
};