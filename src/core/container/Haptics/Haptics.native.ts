import * as ExpoHaptics from 'expo-haptics';
import { ImpactFeedbackStyle } from 'expo-haptics';

class Haptics {
  static ImpactFeedbackStyle = {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy',
    Soft: 'soft',
    Rigid: 'rigid',
  } as const;

  private static instance: Haptics;

  private constructor() {}

  public static async impactAsync(
    style: keyof typeof Haptics.ImpactFeedbackStyle = 'Light',
  ): Promise<void> {
    const styleMap = {
      [ImpactFeedbackStyle.Light]: ExpoHaptics.ImpactFeedbackStyle.Light,
      [ImpactFeedbackStyle.Medium]: ExpoHaptics.ImpactFeedbackStyle.Medium,
      [ImpactFeedbackStyle.Heavy]: ExpoHaptics.ImpactFeedbackStyle.Heavy,
      [ImpactFeedbackStyle.Soft]: ExpoHaptics.ImpactFeedbackStyle.Soft,
      [ImpactFeedbackStyle.Rigid]: ExpoHaptics.ImpactFeedbackStyle.Rigid,
    };
    try {
      await ExpoHaptics.impactAsync(styleMap[style]);
    } catch (error) {
      console.warn('Haptics not supported:', error);
    }
  }
}

export default Haptics;
