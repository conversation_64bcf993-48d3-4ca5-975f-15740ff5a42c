import {useCallback} from "react";
import {Alert, Platform} from "react-native";
import {useRouter} from "expo-router";
import Analytics from "../analytics";
import {ANALYTICS_EVENTS} from "../analytics/const";
import useGoBack from "navigator/hooks/useGoBack"

const useHandleGoBackFromGame = props => {
    const router = useRouter();

    const {goBack} = useGoBack()

    const handleGoBackFromGame = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.CLICKED_ON_GO_BACK_FROM_GAME);
        if(Platform.OS === 'web') {
            goBack()
            return false;
        }

        Alert.alert('Quitting Game?', 'Do you really want to quit this game?', [
            { text: 'No', style: 'cancel', onPress: () => null },
            { text: 'Yes', onPress: () => router.navigate('/home') },
        ]);
        return true;
    }, [router]);

    return {
        handleGoBackFromGame
    }
}

export default useHandleGoBackFromGame;