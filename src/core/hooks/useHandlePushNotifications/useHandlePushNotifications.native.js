import { useEffect, useRef, useState } from 'react';
import * as Notifications from 'expo-notifications';

import { getDeviceToken } from '../../utils/pushNotification';
import useNotificationRedirect from './useNotificationRedirect';
import useRegisterPushNotificationToken from '../useRegisterPushNotificationToken';
import { useSession } from '../../../modules/auth/containers/AuthProvider';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

let hasRegisterPushNotificationToken = false;

const useHandlePushNotifications = () => {
  const { userId } = useSession();
  const [expoPushToken, setExpoPushToken] = useState('');
  const [expoPushTokenError, setExpoPushTokenError] = useState('');
  const [notification, setNotification] = useState(undefined);
  const notificationListener = useRef();
  const responseListener = useRef();

  const { handleDeviceTokenRegistration } = useRegisterPushNotificationToken();

  const handleDeviceTokenRegistrationRef = useRef(
    handleDeviceTokenRegistration,
  );
  handleDeviceTokenRegistrationRef.current = handleDeviceTokenRegistration;

  const { parseAndRedirect } = useNotificationRedirect();

  const parseAndRedirectRef = useRef(parseAndRedirect);
  parseAndRedirectRef.current = parseAndRedirect;

  useEffect(() => {
    let isMounted = true;
    if (hasRegisterPushNotificationToken) {
      return;
    }
    hasRegisterPushNotificationToken = true;

    getDeviceToken({ userId })
      .then((token) => {
        handleDeviceTokenRegistrationRef.current({ deviceToken: token });
      })
      .catch((error) => setExpoPushTokenError(`${error}`));

    notificationListener.current =
      Notifications.addNotificationReceivedListener((notification) => {
        setNotification(notification);
      });

    responseListener.current =
      Notifications.addNotificationResponseReceivedListener((response) => {
        parseAndRedirectRef.current?.(response.notification);
      });

    Notifications.getLastNotificationResponseAsync().then((response) => {
      if (!isMounted || !response?.notification) {
        return;
      }
      parseAndRedirectRef.current?.(response?.notification);
    });

    return () => {
      isMounted = false;
      notificationListener.current &&
        Notifications.removeNotificationSubscription(
          notificationListener.current,
        );
      responseListener.current &&
        Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, []);

  return {
    expoPushToken,
    expoPushTokenError,
    notification,
  };
};

export default useHandlePushNotifications;
