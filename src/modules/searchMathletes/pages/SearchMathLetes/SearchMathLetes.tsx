import React, { useCallback, useEffect, useRef } from 'react';
import { FlatList, Keyboard, SafeAreaView, View } from 'react-native';
import { SearchBar } from '@rneui/base';
import _size from 'lodash/size';
import Header from 'shared/Header';
import useSearchUsers from 'shared/users/hooks/useSearchUsers';
import SearchUserCard from 'modules/friendsAndFollowers/components/SearchUserCard';
import useMediaQuery from 'core/hooks/useMediaQuery';
import styles from './SearchMathLetes.style';

const SearchMathLetes = () => {
  const searchBarRef = useRef(null);
  const { loading, error, users, onSearchQueryChange, searchQuery } =
    useSearchUsers();

  const { isMobile: isCompactMode } = useMediaQuery();

  const renderSeparator = () => <View style={styles.separator} />;

  const renderRow = useCallback(
    ({ item }) => <SearchUserCard infoData={item} />,
    [],
  );

  useEffect(() => {
    if (searchBarRef.current) {
      setTimeout(() => {
        searchBarRef.current?.focus?.();
        searchBarRef.current?.input?.focus();
      }, 150);
    } else {
      Keyboard.dismiss();
    }
  }, []);

  return (
    <SafeAreaView style={{ flex: 1, height: '100%', width: '100%' }}>
      <View style={{ flex: 1, flexDirection: 'column' }}>
        <View style={[styles.inputBoxContainer]}>
          <Header title="Search Mathlete" />
          <View
            style={{
              height: 40,
              width: '100%',
              paddingHorizontal: 16,
              marginTop: isCompactMode ? 0 : 16,
            }}
          >
            <SearchBar
              ref={searchBarRef}
              placeholder="Search Mathlete (atleast 3 char)"
              onChangeText={onSearchQueryChange}
              containerStyle={styles.searchBarContainerStyle}
              inputContainerStyle={styles.inputContainerStyle}
              value={searchQuery}
              inputStyle={styles.inputStyle}
            />
          </View>
        </View>
        <View style={{ flex: 1, marginTop: 10 }}>
          <FlatList
            data={loading || _size(searchQuery) < 3 ? [] : users}
            style={styles.userListStyle}
            keyExtractor={(item) => item.id}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            renderItem={loading || _size(searchQuery) < 3 ? null : renderRow}
            ItemSeparatorComponent={renderSeparator}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default React.memo(SearchMathLetes);
