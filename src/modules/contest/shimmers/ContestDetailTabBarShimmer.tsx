import { View, Animated } from 'react-native';
import { useEffect } from 'react';
import styles from './ContestDetailTabBarShimmer.style'
import  { width } from 'react-native';
import LinearGradient from '../../../components/atoms/LinearGradient';
const ContestTabBarShimmer = () => {
    const animatedValue = new Animated.Value(0);
  
    useEffect(() => {
      Animated.loop(
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        })
      ).start();
    }, []);
  
    const translateX = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [-width, width],
    });
  
    return (
      <View style={styles.container}>
        <View style={styles.tabBarContainer}>
          <View style={styles.tabBar}>
            {[0, 1, 2].map((_, index) => (
              <View key={index} style={styles.tab}>
                <View style={styles.tabLabel} />
              </View>
            ))}
          </View>
          <View style={styles.indicator} />
        </View>
        <View style={styles.contentContainer}>
          <View style={styles.contentLine} />
          <View style={styles.contentLine} />
          <View style={styles.contentLine2} />
          <View style={styles.contentLine3} />
        </View>
        <Animated.View
          style={[
            styles.shimmerOverlay,
            {
              transform: [{ translateX }],
            },
          ]}
        >
          <LinearGradient
            colors={['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 0.3)', 'rgba(255, 255, 255, 0)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={{ flex: 1 }}
          />
        </Animated.View>
      </View>
    );
  };
  

  export default ContestTabBarShimmer