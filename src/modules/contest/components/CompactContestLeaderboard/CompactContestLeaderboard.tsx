import React, { useCallback } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import { useRouter } from 'expo-router';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import contestReader from 'core/readers/contestReader';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView/ErrorView';
import Analytics from 'core/analytics';
import userReader from 'core/readers/userReader';
import CompactUserScoreCard from 'modules/contest/components/CompactUserScoreCard';
import LeaderboardItem from 'modules/contest/components/LeaderboardItem';
import useContestLeaderBoard from '../../hooks/useContestLeaderboard';
import styles from '../CompactUserScoreCard/CompactUserScoreCard.style';
import { getContestPropertiesToTrack } from '../../utils/contestEvents';
import TakeVirtualContestCard from '../TakeVirtualContestCard';

const CompactContestLeaderboard = ({ contest }: { contest: any }) => {
  const router = useRouter();
  const pageSize = 3;
  const { loading, error, hasMore, participants } = useContestLeaderBoard({
    contest,
    pageSize,
  });

  const contestId = contestReader.id(contest);

  const onPressSeeAll = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.CONTEST.CLICKED_ON_CONTEST_DETAIL_PAGE_ALL_RESULT,
      {
        ...getContestPropertiesToTrack({ contest }),
        [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
      },
    );
    router.push(`/contest/leaderboard/${contestId}`);
  }, [contest, router, contestId]);

  const renderLeaderboardItem = useCallback(
    ({ item, index }: { item: any; index: number }) => (
      <View style={{ alignItems: 'center' }}>
        <LeaderboardItem
          name={userReader.username(item?.user)}
          score={item.score}
          position={index + 1}
        />
      </View>
    ),
    [],
  );

  if (loading) {
    return <Loading label="Loading Leaderboard..." />;
  }

  if (error) {
    return <ErrorView errorMessage="Oops, Leaderboard is Empty" />;
  }

  if (_isEmpty(participants)) {
    return (
      <View style={{ alignItems: 'center', marginTop: 0, flex: 1 }}>
        <TakeVirtualContestCard contestId={contestId} />
        <View
          style={{
            flex: 1,
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text style={{ color: 'white' }}> Leaderboard is Empty</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <FlatList
        data={participants}
        renderItem={renderLeaderboardItem}
        ListHeaderComponent={<CompactUserScoreCard contest={contest} />}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => `${item?._id}-${index}`}
        ListFooterComponent={
          hasMore && (
            <TouchableOpacity onPress={onPressSeeAll}>
              <Text style={styles.footerText}>See All</Text>
            </TouchableOpacity>
          )
        }
      />
    </View>
  );
};

export default React.memo(CompactContestLeaderboard);
