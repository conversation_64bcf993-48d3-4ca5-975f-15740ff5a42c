import React, { useEffect, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';

import _isNil from 'lodash/isNil';
import useContestBannerStyles from './ContestBanner.style';

const ensureMinDigits = (num, minDigits) => {
  let numString = `${num}`;
  while (numString.length < minDigits) {
    numString = `0${numString}`;
  }
  return numString;
};

const LiveContestTimer = ({ contest }) => {
  const { endTime } = contest;

  const styles = useContestBannerStyles();

  const [timeLeft, setTimeLeft] = useState();

  useEffect(() => {
    const interval = setInterval(() => {
      const now = getCurrentTimeWithOffset();
      const timeLeft = Math.max(0, new Date(endTime).getTime() - now);
      setTimeLeft(timeLeft);
    }, 1000);

    return () => clearInterval(interval);
  }, [endTime]);

  const renderLiveTimer = ({ label, value }) => (
    <View style={styles.singleTimerContainer}>
      <View style={styles.timerValueContainer}>
        <Text style={styles.timerValue}>{ensureMinDigits(value, 2)}</Text>
      </View>
      <Text style={styles.timerLabel}>{label}</Text>
    </View>
  );

  const hourLeft = Math.floor(timeLeft / 1000 / 60 / 60);
  const minuteLeft = Math.floor((timeLeft / 1000 / 60) % 60); // Get the minutes left
  const secondLeft = Math.floor((timeLeft / 1000) % 60); // Get the seconds left

  if (_isNil(timeLeft)) {
    return null;
  }

  return (
    <View style={styles.contentContainer}>
      <Text style={styles.text80In8Live}>80 IN 8 IS LIVE!</Text>
      <View style={styles.timerContainer}>
        {renderLiveTimer({ label: 'hours', value: hourLeft })}
        <View style={styles.colonContainer}>
          <View style={styles.colonDot} />
          <View style={styles.colonDot} />
        </View>
        {renderLiveTimer({ label: 'minutes', value: minuteLeft })}
        <View style={styles.colonContainer}>
          <View style={styles.colonDot} />
          <View style={styles.colonDot} />
        </View>
        {renderLiveTimer({ label: 'seconds', value: secondLeft })}
      </View>
      <Pressable style={styles.registerNowButton}>
        <View>
          <Text style={styles.registerNowText}>COMPETE NOW</Text>
        </View>
      </Pressable>
    </View>
  );
};

export default React.memo(LiveContestTimer);
