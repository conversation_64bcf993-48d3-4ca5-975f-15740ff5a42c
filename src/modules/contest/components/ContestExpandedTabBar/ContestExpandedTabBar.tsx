import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { Dimensions, ScrollView, Text, View } from 'react-native';
import { TabBar, TabView } from 'react-native-tab-view';

import dark from 'core/constants/themes/dark';
import _isEmpty from 'lodash/isEmpty';
import Analytics from 'core/analytics';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';

import styles from './ContestExpandedTabBar.style';
import ContestScoreAnalysis from 'modules/contest/components/ScoreAnalysis/ContestScoreAnalysis';

import useContestDetailsController from 'modules/contest/hooks/useContestDetailsController';
import {
  TAB_KEY_VS_EVENT,
  TAB_KEYS,
} from 'modules/contest/constants/contestDetails';
import { getContestPropertiesToTrack } from 'modules/contest/utils/contestEvents';
import ContestantsRankInfo from 'modules/contest/pages/ContestLeaderBoard/ContestantsRankInfo';
import ContestTabBarShimmer from 'modules/contest/shimmers/ContestDetailTabBarShimmer';
import ExpandableContainer from 'modules/contest/components/ExpandableContainer';

interface ContestTabBarProps {
  contestId: string;
  contestDetails: any;
  loading: boolean;
  error: boolean;
  refetch: () => void;
}

interface TabsRouteProps {
  contestDetails: any;
}

const AnalysisRoute = ({ contestDetails }: TabsRouteProps) => (
  <ContestScoreAnalysis contestDetails={contestDetails} />
);

const DetailsRoute = ({ contestDetails }: TabsRouteProps) => (
  <ScrollView
    style={styles.mainContainer}
    showsVerticalScrollIndicator={false}
    showsHorizontalScrollIndicator={false}
  >
    <ExpandableContainer
      title="About"
      content={contestDetails?.details?.about}
    />
    <ExpandableContainer
      title="Instructions"
      content={contestDetails?.details?.instructions}
    />
    <ExpandableContainer
      title="Requirements"
      content={contestDetails?.details?.requirements}
    />
  </ScrollView>
);

const initialLayout = { width: Dimensions.get('window').width };

const ContestExpandedTabBar = ({
  contestId,
  contestDetails,
  loading,
  error,
  refetch,
}: ContestTabBarProps) => {
  const [index, setIndex] = useState(0);
  const { isLive, hasEnded, hasUserRegistered } = useContestDetailsController({
    contest: contestDetails,
    refetch,
  });

  const routes = useMemo(() => {
    const availableRoutes = [];
    if (isLive || hasEnded) {
      availableRoutes.push({
        key: TAB_KEYS.RESULT,
        title: isLive ? 'Live Leaderboard' : 'Results',
      });
    }
    if (hasUserRegistered && hasEnded) {
      availableRoutes.push({ key: TAB_KEYS.ANALYSIS, title: 'Analysis' });
    }
    availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
    return availableRoutes;
  }, [isLive, hasUserRegistered, hasEnded]);

  const onIndexChange = useCallback(
    (updatedIndex: number) => {
      setIndex(updatedIndex);
      const route = routes[updatedIndex];
      if (TAB_KEY_VS_EVENT?.[route?.key]) {
        Analytics.track(TAB_KEY_VS_EVENT[route.key], {
          ...getContestPropertiesToTrack({ contest: contestDetails }),
          [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
        });
      }
    },
    [routes, contestDetails],
  );

  const renderScene = useCallback(
    ({ route }: { route: any }) => {
      switch (route?.key) {
        case 'result':
          return <ContestantsRankInfo contest={contestDetails} />;
        case 'analysis':
          return <AnalysisRoute contestDetails={contestDetails} />;
        case 'details':
          return <DetailsRoute contestDetails={contestDetails} />;
      }
    },
    [contestDetails],
  );

  const renderTabBar = (props: any) =>
    routes.length > 1 ? (
      <View style={styles.tabBarContainer}>
        <TabBar
          {...props}
          indicatorStyle={styles.indicator}
          style={styles.tabBar}
          tabStyle={styles.tabStyle}
          labelStyle={styles.label}
          activeColor={dark.colors.secondary}
          inactiveColor={dark.colors.textDark}
          renderLabel={({ route, color }) => (
            <Text style={{ ...styles.label, color }}>{route.title}</Text>
          )}
        />
        <View style={styles.fullWidthLine} />
      </View>
    ) : (
      <View style={{ height: 20 }} />
    );

  if (loading && _isEmpty(contestDetails)) {
    return <ContestTabBarShimmer />;
  }

  return (
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={onIndexChange}
      initialLayout={initialLayout}
      renderTabBar={renderTabBar}
    />
  );
};

export default React.memo(ContestExpandedTabBar);
