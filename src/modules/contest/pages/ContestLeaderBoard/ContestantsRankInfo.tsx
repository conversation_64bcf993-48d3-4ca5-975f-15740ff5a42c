import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import userReader from 'core/readers/userReader';
import contestReader from 'core/readers/contestReader';
import PaginatedList from 'shared/PaginatedList';
import PlaceholderRow from 'shared/PlaceholderRow';
import LeaderBoardHeader from './components/LeaderBoardHeader';
import styles from './ContestLeaderBoard.style';
import ContestLeaderboardRow from './components/ContestLeaderboardRow/ContestLeaderboardRow';
import UserScoreInContestRow from './components/UserScoreInContestRow';
import useGetContestLeaderboard from '../../hooks/useGetContestLeaderboard';
import { CONTEST_STATUS } from '../../constants';

const PAGE_SIZE = 50;

const ContestantsRankInfo = ({ contest }: { contest: any }) => {
  const contestId = contestReader.id(contest);
  const contestStatus = contestReader.status(contest);
  const isContestLive = contestStatus === CONTEST_STATUS.ONGOING;
  const { fetchLeaderboard } = useGetContestLeaderboard({
    contestId,
    isLive: isContestLive,
    pageSize: PAGE_SIZE,
  });

  const fetchData = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      const response = await fetchLeaderboard({ pageNumber });
      const { data } = response ?? EMPTY_OBJECT;
      const { getContestLeaderboard: participantsObject } =
        data ?? EMPTY_OBJECT;
      const { participants, totalParticipants } =
        participantsObject ?? EMPTY_OBJECT;
      return { data: participants, totalItems: totalParticipants };
    },
    [fetchLeaderboard],
  );

  const renderHeader = ({ page }: { page: number }) => (
    <View style={{ width: '100%', paddingHorizontal: 14 }}>
      <LeaderBoardHeader headerStyle={[styles.headerContainer]} />
      {page === 1 && <UserScoreInContestRow contest={contest} />}
    </View>
  );

  const renderRow = useCallback(
    ({ item }: { item: any }) => (
      <View style={{ marginHorizontal: 15 }}>
        <ContestLeaderboardRow participantSubmission={item} contest={contest} />
        <View style={styles.separator} />
      </View>
    ),
    [contest],
  );

  const renderPlaceholderRow = useCallback(
    () => (
      <View style={{ marginHorizontal: 15 }}>
        <PlaceholderRow />
        <PlaceholderRow />
        <PlaceholderRow />
      </View>
    ),
    [],
  );

  const emptyLeaderboardComponent = useCallback(
    () => (
      <View style={styles.emptyLeaderboardContainer}>
        <Text style={styles.emptyLeaderboardLabel}>Leaderboard is empty</Text>
      </View>
    ),
    [],
  );

  return (
    <View style={styles.container}>
      <PaginatedList
        placeholderComponent={renderPlaceholderRow}
        key="contestantsRankInfo"
        fetchData={fetchData}
        renderItem={renderRow}
        renderHeader={renderHeader}
        emptyListComponent={emptyLeaderboardComponent}
        makeHeaderScrollable={false}
        pageSize={PAGE_SIZE}
        keyExtractor={(item, index) =>
          `${userReader.id(item?.user)} - ${index}`
        }
        listFooterComponent={<View style={{ height: 80 }} />}
      />
    </View>
  );
};

export default React.memo(ContestantsRankInfo);
