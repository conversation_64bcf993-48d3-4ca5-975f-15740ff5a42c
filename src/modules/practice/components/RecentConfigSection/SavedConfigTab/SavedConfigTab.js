import PropTypes from 'prop-types';
import { FlatList, Text } from 'react-native';
import { View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import RecentConfigCard from '../../RecentConfigCard/RecentConfigCard';
import useRecentConfigTabStyles from '../RecentConfigTab/RecentConfigTab.style';
import Loading from 'atoms/Loading';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useUserStore from 'store/useUserStore';
import { useEffect } from 'react';
import useNetworkStatus from 'core/hooks/useNetworkStatus';
import ErrorView from 'atoms/ErrorView';

const SavedConfigTab = (props) => {
  const styles = useRecentConfigTabStyles();

  const { controller } = props;

  const { isMobile: isCompactMode } = useMediaQuery();
  const { isNetworkReachable } = useNetworkStatus();

  const { fetchUserSavedPresets, userPresets, loading, error } = useUserStore(
    (state) => ({
      fetchUserSavedPresets: state.fetchUserSavedPresets,
      userPresets: state.savedPresets,
      loading: state.savedPresetsLoading,
      error: state.savedPresetsError,
    }),
  );

  useEffect(() => {
    if (isNetworkReachable) {
      fetchUserSavedPresets();
    }
  }, [fetchUserSavedPresets, isNetworkReachable]);

  if (loading) {
    return <Loading label={'Loading'} />;
  }

  if (error && isNetworkReachable) {
    return (
      <ErrorView
        isHeaderVisible={false}
        errorMessage="An error occurred while connecting to server."
        onRetry={fetchUserSavedPresets}
      />
    );
  }

  if (_isEmpty(userPresets)) {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <Text style={{ fontFamily: 'Montserrat-500', color: 'white' }}>
          No Saved Presets !!
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      keyExtractor={(item, index) => `${item?._id} - ${index}`}
      contentContainerStyle={{ gap: 14, padding: 16 }}
      renderItem={({ item }) => (
        <RecentConfigCard
          presetInfo={item}
          controller={controller}
          isRecentTab={false}
        />
      )}
      data={userPresets}
      numColumns={isCompactMode ? 1 : 2}
      showsVerticalScrollIndicator={false}
      columnWrapperStyle={isCompactMode ? null : styles.columnWrapperStyle}
    />
  );
};

SavedConfigTab.propTypes = {
  savedConfigItem: PropTypes.array,
  controller: PropTypes.object,
};

export default SavedConfigTab;
