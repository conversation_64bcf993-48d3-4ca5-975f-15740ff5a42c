import PropTypes from 'prop-types'
import React, { useCallback, useMemo } from 'react'
import { Platform, Text, View } from 'react-native'
import _map from 'lodash/map'
import _isEmpty from 'lodash/isEmpty'
import _isString from 'lodash/isString'
import _includes from 'lodash/includes'
import _size from 'lodash/size'
import _split from 'lodash/split'
import NumberToSvg from 'atoms/NumberToSvg'
import usePracticeQuestionStyles from './PracticeQuestion.style'
import getTextStyleAccToConfig from '../../utils/getFontSizeAccToConfig'

const ALL_OPERATORS = ['+', '-', '×', '*', '÷']
const EMPTY_OBJECT = {}

const PracticeQuestion = (props) => {
  const styles = usePracticeQuestionStyles()
  const isNativeDevice = Platform.OS !== 'web'
  const { currentQuestion } = props

  let maxDigits = 0

  const { rows, question: expression } = currentQuestion ?? EMPTY_OBJECT

  const { fontSize, gapBetweenRows } = useMemo(
    () => getTextStyleAccToConfig({ noOfRows: rows }),
    [rows]
  )

  const renderExpressionRow = useCallback(
    ({ operator = '', number }, index) => (
      <View key={`${operator}_${number}_${index}`} style={styles.expressionRow}>
        <View style={styles.digitContainer}>
          {_map(
            new Array(Math.max(0, maxDigits - _size(_split(number, '')))).fill(
              ' '
            ),
            (digit, digitIndex) => (
              <View
                key={`digit_${digitIndex}`}
                style={[
                  styles.digitBox,
                  { height: fontSize + 2 },
                  isNativeDevice && { width: fontSize + 2 },
                ]}
              >
                <Text
                  allowFontScaling={false}
                  style={[styles.questionExpression, { fontSize }]}
                >
                  {' '}
                </Text>
              </View>
            )
          )}
          <View
            style={[
              styles.digitBox,
              // { height: fontSize + 2 },
              isNativeDevice && { width: fontSize + 2 },
            ]}
          >
            <Text
              allowFontScaling={false}
              style={[styles.questionExpression, { fontSize }]}
            >
              {operator}
            </Text>
          </View>
          {_map(_split(number, ''), (digit, digitIndex) => (
            <View
              key={`digit_${digitIndex}`}
              style={[
                styles.digitBox,
                // { height: fontSize + 2 },x
                isNativeDevice && { width: fontSize + 2 },
              ]}
            >
              <Text
                allowFontScaling={false}
                style={[styles.questionExpression, { fontSize }]}
              >
                <NumberToSvg number={digit} fontSize={fontSize} />
              </Text>
            </View>
          ))}
        </View>
      </View>
    ),
    [rows, maxDigits, fontSize]
  )

  const groupedExpression = useMemo(() => {
    const groupedExpressionArray = []
    for (let i = 0; i < _size(expression); ) {
      const obj = { operator: '', number: '' }
      if (_includes(ALL_OPERATORS, expression[i])) {
        obj.operator = expression[i]
        obj.number = expression[i + 1]
        i += 2
      } else {
        obj.number = expression[i]
        i += 1
      }
      maxDigits = Math.max(maxDigits, _size(_split(obj.number, '')))
      groupedExpressionArray.push(obj)
    }
    return groupedExpressionArray
  }, [expression])

  if (_isEmpty(currentQuestion)) return null

  if (_isString(expression)) {
    return (
      <Text allowFontScaling={false} style={styles.questionExpression}>
        {expression}
      </Text>
    )
  }

  return (
    <View style={[styles.expressionContainer, { gap: gapBetweenRows }]}>
      {_map(groupedExpression, renderExpressionRow)}
    </View>
  )
}

PracticeQuestion.propTypes = {
  currentQuestion: PropTypes.object,
}

export default PracticeQuestion
