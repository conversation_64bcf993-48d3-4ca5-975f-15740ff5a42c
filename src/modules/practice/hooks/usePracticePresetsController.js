import { useCallback, useState } from "react";
import Analytics from "../../../core/analytics";
import { ANALYTICS_EVENTS } from "../../../core/analytics/const";
import { PAGE_NAME_KEY, PAGE_NAMES } from "../../../core/constants/pageNames";
import { PRACTICE_LEFT_PANE_TABS, PRACTICE_LEFT_PANE_TABS_CONFIGS } from "../pages/Practice/components/OperatorSelector/constants/practice";
import { useStorageState } from "core/hooks/useStorageState";

import _filter from 'lodash/filter'
import _isEmpty from 'lodash/isEmpty'
import _findIndex from 'lodash/findIndex'
import _compact from 'lodash/compact'

import { PRACTICE_PRESET_UPDATE_INFO_MODAL_SHOWN } from "core/constants/appConstants";

const usePracticePresetsController = () => {

    const [isPracticeConfgShown, setPracticeConfigUpdateModalShown] = useStorageState(PRACTICE_PRESET_UPDATE_INFO_MODAL_SHOWN);

    const [showUpdateModal, setShowUpdateModal] = useState(false)

    const [selectedTabId, setSelectedTabId] = useState(PRACTICE_LEFT_PANE_TABS.RECENT_AND_SAVED);

    const selectedTabConfig = PRACTICE_LEFT_PANE_TABS_CONFIGS[selectedTabId];

    const [selectedPresets, setSelectedPresets] = useState([]);

    const onUnderStoodPressed = useCallback(() => {
        setShowUpdateModal(false)
        setPracticeConfigUpdateModalShown("true");
    }, [setPracticeConfigUpdateModalShown, setShowUpdateModal])

    const onLeftPaneTabChange = useCallback((tabId) => {
        setSelectedTabId(tabId);
    }, [setSelectedTabId])

    // TODO:  send operationId in the args. 
    const addPreset = useCallback((preset) => {
        Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.ADDING_PRACTICE_CATEGORY_CONFIG, {
            [PAGE_NAME_KEY]: PAGE_NAMES.PRACTICE_PAGE,
            category: selectedTabConfig?.operationName,
            ...preset.config,
        })
        setSelectedPresets((prevPresets) => [...prevPresets, preset])

    }, [setSelectedPresets, selectedTabConfig?.operationName, selectedTabId])

    const removePreset = useCallback((presetId) => {
        if (_isEmpty(presetId)) {
            return
        }
        setSelectedPresets(prevPresets => _filter(prevPresets, preset => preset.id !== presetId))

    }, [setSelectedPresets]);

    const updatePreset = useCallback(({ presetId, udpatedPresetConfig }) => {
        if (_isEmpty(presetId)) {
            return
        }
        if (isPracticeConfgShown !== "true") {
            setShowUpdateModal(true)
        }
        setSelectedPresets(prevPresets => {
            const index = _findIndex(prevPresets, preset => preset.id === presetId);

            if (index === -1) {
                return 
            }

            const updatedPresets = [...prevPresets];
            const { config: pvsConfig, ...currentPresetWithoutConfig } = updatedPresets[index];
            updatedPresets[index] = {
                ...currentPresetWithoutConfig,
                config: udpatedPresetConfig
            };
            return updatedPresets;
        });
    }, [setShowUpdateModal, isPracticeConfgShown, setSelectedPresets])

    return {
        selectedTabId,
        showUpdateModal,
        allSelectedPresets: _compact(selectedPresets),
        updatePreset,
        addPreset,
        removePreset,
        onLeftPaneTabChange,
        onUnderStoodPressed
    }
}

export default usePracticePresetsController;