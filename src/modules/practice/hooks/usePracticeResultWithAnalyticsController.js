import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import _isEmpty from "lodash/isEmpty"
import _map from "lodash/map"
import _forEach from 'lodash/forEach'
import _reduce from 'lodash/reduce'
import _filter from 'lodash/filter'
import _size from "lodash/size"
import { useRouter } from "expo-router";
import {
    DMAS_CATEGORIES,
    PRACTICE_CATEGORIES,
    PRACTICE_LEFT_PANE_TABS_CONFIGS
} from "../pages/Practice/components/OperatorSelector/constants/practice";
import Analytics from "../../../core/analytics";
import { ANALYTICS_EVENTS } from "../../../core/analytics/const";
import { PAGE_NAME_KEY, PAGE_NAMES } from "../../../core/constants/pageNames";
import _maxBy from 'lodash/maxBy'
import _minBy from 'lodash/minBy'
import _toString from 'lodash/toString'
import _findIndex from 'lodash/findIndex'
import { getConfigTagsFromIdentifier, getIdentifierStringFromConfig } from "../utils/getIdentifierStringFromConfig";
import useSubmitUserPresetResultMutation from "./mutations/useSubmitUserPresetResultMutation";
import useGetUserSavedPresets from "./queries/useGetSavedPresetQuery";
import useGetRecentPresetsQuery from "./queries/useGetRecentPresetsQuery";
import _includes from "lodash/includes";
import _sum from "lodash/sum"
import useUserActivityTracker from "../../../core/hooks/useUserActivityTracker";
import ACTIVITY_TYPES from "@/src/core/constants/activityTypes";


const getConfigAndQueText = ({ config, tag }) => {
    const { noOfDigitsRow1, noOfDigitsRow2, noOfQuestions, rows, digits } = config
    if (tag === PRACTICE_CATEGORIES.MULTIPLICATION) {
        return `${noOfDigitsRow1}D x ${noOfDigitsRow2}D , ${noOfQuestions} Questions`
    } if (tag === PRACTICE_CATEGORIES.DIVISION) {
        return `${noOfDigitsRow1}D ÷ ${noOfDigitsRow2}D , ${noOfQuestions} Questions`
    }
    return `${digits}D ${rows}R , ${noOfQuestions} Questions`
}

const usePracticeResultWithAnalyticsController = ({ sessionData, selectedPresets }) => {
    const [isResultSaved, setIsResultSaved] = useState(false)
    const { reFetchUserRecentPresets } = useGetRecentPresetsQuery();
    const { updateActivity } = useUserActivityTracker()

    const { userSubmissions, totalTime } = sessionData ?? EMPTY_OBJECT
    const router = useRouter()

    const { loading, error, userPresets } = useGetUserSavedPresets({ page: 1, pageSize: 100 })

    const savedPresetsIdentifier = useMemo(() => {
        return _map(userPresets, (preset, index) => {
            return {
                identifier: _toString(preset.identifier),
                name: _toString(preset.name)
            }
        })
    }, [userPresets])

    const handlePlayAgainPressed = useCallback(() => {
        const { categoryId } = selectedPresets?.[0];
        const { tag } = PRACTICE_LEFT_PANE_TABS_CONFIGS[categoryId]

        const isDmasTag = _includes(DMAS_CATEGORIES, tag);

        Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.CLICKED_ON_PRACTICE_AGAIN, {
            [PAGE_NAME_KEY]: PAGE_NAMES.PRACTICE_RESULT,
        })
        if (isDmasTag) {
            router.replace('/nets/play')
        } else {
            router.replace(`/nets/${tag}`);
        }
    }, [router, selectedPresets]);

    const filteredAnalyticsData = useMemo(() => {
        if (_isEmpty(selectedPresets)) {
            return EMPTY_OBJECT;
        }
        const presetAnalytics = [];

        _forEach(selectedPresets, (preset, index) => {
            const { categoryId, config, id: presetId } = preset
            const { operationName, tag, id } = PRACTICE_LEFT_PANE_TABS_CONFIGS[categoryId]

            const presetQuestions = _filter(userSubmissions, (submission) => submission?.question?.presetId === presetId);
            const submittedTimes = _map(presetQuestions, (que, index) => Math.ceil(que.timeTaken * 1000))
            const totalTime = _reduce(presetQuestions, (acc, q) => acc + q.timeTaken, 0);
            const solvedCount = _filter(presetQuestions, q => q.isCorrect).length;
            const fastestTimeQue = _minBy(presetQuestions, (que) => que.timeTaken)
            const slowestTimeQue = _maxBy(presetQuestions, (que) => que.timeTaken)
            const identifier = getIdentifierStringFromConfig({ config, tag })
            const incorrectAttempts = _map(presetQuestions, (que) => que.incorrectAttempts)


            const indexOfSavedIdentifier = _findIndex(savedPresetsIdentifier, ide => ide.identifier === identifier)
            const isSaved = indexOfSavedIdentifier !== -1

            presetAnalytics.push({
                key: presetId,
                name: isSaved ? savedPresetsIdentifier[indexOfSavedIdentifier].name : `Preset - ${index + 1}`,
                config: getConfigAndQueText({ tag: tag, config: config }),
                totalTime,
                identifier: identifier,
                solvedCount,
                avgTime: totalTime / solvedCount,
                fastestTime: fastestTimeQue?.timeTaken,
                slowestTime: slowestTimeQue?.timeTaken,
                configTags: getConfigTagsFromIdentifier({ identifier }),
                totalQuestions: presetQuestions.length,
                presetQuestions: presetQuestions,
                isSaved: isSaved,
                submittedTimes: submittedTimes,
                incorrectAttempts
            });
        });

        return presetAnalytics;

    }, [selectedPresets, userSubmissions, savedPresetsIdentifier])

    const getUserPresetResultArrayToSave = useCallback(() => {
        if (_isEmpty(selectedPresets)) {
            return EMPTY_OBJECT;
        }
        const presetResult = [];

        _forEach(selectedPresets, (preset, index) => {
            const { categoryId, config, id: presetId } = preset
            const { tag } = PRACTICE_LEFT_PANE_TABS_CONFIGS[categoryId]

            const presetQuestions = _filter(userSubmissions, (submission) => submission?.question?.presetId === presetId);

            const submittedTimes = _map(presetQuestions, (que, index) => Math.ceil(que.timeTaken * 1000))
            const incorrectAttempts = _map(presetQuestions, (que, index) => que.incorrectAttempts)

            presetResult.push({
                date: new Date(),
                identifier: getIdentifierStringFromConfig({ config, tag }),
                numOfQuestions: presetQuestions.length,
                incorrectAttempts: incorrectAttempts,
                submittedTimes: submittedTimes,
                savedConfig: JSON.stringify(config)
            });
        });

        return presetResult;
    }, [selectedPresets, userSubmissions])


    const { submitUserPresetResultMutation } = useSubmitUserPresetResultMutation()

    const saveUserPresetResult = useCallback(async () => {
        try {
            const userPresetResultInput = getUserPresetResultArrayToSave()

            for (let i = 0; i < _size(userPresetResultInput); i++) {
                const resultItem = userPresetResultInput[i]
                const totalTimeSpent = _sum(resultItem?.submittedTimes)
                updateActivity({
                    activityType: ACTIVITY_TYPES.PRACTICE_NETS,
                    duration: totalTimeSpent
                })
            }

            await submitUserPresetResultMutation({ userPresetResultInput })
            reFetchUserRecentPresets();
        } catch (e) {
            console.info(e)
        }
    }, [getUserPresetResultArrayToSave, submitUserPresetResultMutation, updateActivity]);

    const saveUserPresetResultRef = useRef(saveUserPresetResult);
    saveUserPresetResultRef.current = saveUserPresetResult;

    useEffect(() => {
        if (!isResultSaved) {
            setIsResultSaved(true)
            saveUserPresetResultRef.current()
        }
    }, [])

    return {
        userSubmissions,
        totalTime,
        filteredAnalyticsData,
        handlePlayAgainPressed,
        getUserPresetResultArrayToSave
    }
}

export default usePracticeResultWithAnalyticsController