import { useRouter } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { generateMathQuestions } from 'core/utils/questionGenerator/quesitionGenerator';

import _isEmpty from 'lodash/isEmpty';
import _toString from 'lodash/toString';
import _findIndex from 'lodash/findIndex';
import _get from 'lodash/get';
import getCurrentTimeWithOffset from '../../../core/utils/getCurrentTimeWithOffset';
import { usePracticeContext } from '../../../../app/_layout';

const getInitialGameState = (selectedPresets) => {
  const generatedQuestions = generateMathQuestions(selectedPresets);

  return {
    questions: generatedQuestions,
    questionStartTime: getCurrentTimeWithOffset(),
    isCompleted: false,
    currentQuestionIndex: 0,
    userAnswer: '',
    userSubmissions: [],
    totalTimeTaken: 0,
  };
};

const createQuestionSubmission = ({
  question,
  userAnswer,
  timeTaken,
  isCorrect,
  incorrectAttempts,
}) => ({
  question,
  userAnswer,
  timeTaken,
  isCorrect,
  incorrectAttempts,
  timestamp: new Date().toISOString(),
});

const usePracticePlayGameController = ({ selectedPresets }) => {
  const router = useRouter();

  const [focusAgain, setFocusAgain] = useState(false);
  const [gameState, setGameState] = useState(
    getInitialGameState(selectedPresets),
  );

  const [timer, setGameTimer] = useState(0);

  const { savePracticeSession } = usePracticeContext();

  const {
    questions,
    currentQuestionIndex,
    userSubmissions,
    questionStartTime,
  } = gameState;

  const intervalRef = useRef(null);

  const currentQuestion = questions[currentQuestionIndex];

  const { answers } = currentQuestion ?? EMPTY_OBJECT;

  const isLastQuestion = currentQuestionIndex === questions.length - 1;

  const updateGameState = useCallback(
    (updater) => {
      setGameState(updater);
    },
    [setGameState],
  );

  const submitAnswer = useCallback(
    ({ questionId, value, incorrectAttempts }) => {
      const currentTime = getCurrentTimeWithOffset();
      const timeTakenForQuestion = (currentTime - questionStartTime) / 1000;

      const newSubmission = createQuestionSubmission({
        question: currentQuestion,
        userAnswer: value,
        timeTaken: timeTakenForQuestion,
        isCorrect:
          _toString(_get(currentQuestion, ['answers', 0])) === _toString(value),
        incorrectAttempts,
      });

      if (isLastQuestion) {
        const finalSubmissions = [...userSubmissions, newSubmission];
        const sessionData = {
          userSubmissions: finalSubmissions,
          totalTime: timer,
          totalQuestions: questions.length,
        };

        updateGameState((prev) => ({
          ...prev,
          isCompleted: true,
          userSubmissions: finalSubmissions,
          totalTimeTaken: timer,
        }));
        clearInterval(intervalRef.current);
        savePracticeSession({ sessionData });
        router.replace('/nets/result', null);
        return;
      }

      updateGameState((prev) => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex + 1,
        userSubmissions: [...prev.userSubmissions, newSubmission],
        questionStartTime: currentTime,
      }));
    },
    [
      isLastQuestion,
      currentQuestion,
      questionStartTime,
      timer,
      userSubmissions,
      questions,
      savePracticeSession,
      updateGameState,
    ],
  );

  const startTimer = useCallback(() => {
    const currentTime = getCurrentTimeWithOffset();
    updateGameState((prev) => ({
      ...prev,
      questionStartTime: currentTime,
    }));

    intervalRef.current = setInterval(() => {
      setGameTimer((prev) => prev + 1);
    }, 1000);
  }, []);

  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  }, []);

  const toggleTypingDirection = useCallback(
    (direction) => {
      if (_isEmpty(currentQuestion)) {
        return;
      }

      const { id: currQuestionId } = currentQuestion;

      updateGameState((prev) => {
        const questionIndex = _findIndex(
          prev.questions,
          (question) => question.id === currQuestionId,
        );
        const { questions } = prev;
        questions[questionIndex].typingDirection = direction;

        return {
          ...prev,
          questions,
        };
      });

      setFocusAgain((prev) => !prev);
    },
    [setFocusAgain, currentQuestion],
  );

  const startTimerRef = useRef(startTimer);
  startTimerRef.current = startTimer;

  const submitAnswerRef = useRef(submitAnswer);
  submitAnswerRef.current = submitAnswer;

  useEffect(() => {
    startTimerRef.current();
    return () => clearInterval(intervalRef.current);
  }, []);

  return {
    questions,
    focusAgain,
    currentQuestion,
    currentQuestionIndex,
    submitAnswerRef,
    timer,
    answer: _get(currentQuestion, ['answers', 0]),
    toggleTypingDirection,
    formatTime,
  };
};

export default usePracticePlayGameController;
