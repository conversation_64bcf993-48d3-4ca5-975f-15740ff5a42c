import { <PERSON><PERSON><PERSON>, FlatList, ScrollView, View } from "react-native"
import usePracticeResultStyles from "./PracticeResult.style"
import Header from 'shared/Header'
import { Text } from "react-native"
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useCallback, useEffect, useState } from "react"
import { getFormattedTimeWithMS } from "../../../../core/utils/general"
import { TouchableOpacity } from "react-native"
import PropTypes from "prop-types"
import dark from "../../../../core/constants/themes/dark"
import usePracticeResultWithAnalyticsController from "../../hooks/usePracticeResultWithAnalyticsController"

import { router, useRouter } from "expo-router"
import { TabBar, TabView } from "react-native-tab-view"
import ResultAnalyticsInfoWithConfigCard from "./components/ResultAnalyticsInfoByPresetId"
import { Icon } from "@rneui/themed"
import QuestionView from "./components/QuestionView/QuestionView"
import Analytics from "../../../../core/analytics";
import {ANALYTICS_EVENTS} from "../../../../core/analytics/const";
import {PAGE_NAME_KEY, PAGE_NAMES} from "../../../../core/constants/pageNames";

const initialLayout = { width: Dimensions.get('window').width }

const PracticeReultPage = (props) => {

    const [index, setIndex] = useState(0)
    const router = useRouter()
    const { isMobile: isCompactMode } = useMediaQuery()
    const styles = usePracticeResultStyles()
    const { practiceSession, practiceConfig } = props
    const { selectedPresets } = practiceConfig ?? EMPTY_OBJECT
    const { sessionData } = practiceSession ?? EMPTY_OBJECT

    const onIndexChange = useCallback((updatedIndex) => {
        setIndex(updatedIndex);
    }, [setIndex]);

    const onPressViewQuestions = useCallback(() => {
        router.push(`/nets/result?questionNo=0`)
    }, [router])

    const { userSubmissions, totalTime, filteredAnalyticsData, handlePlayAgainPressed } = usePracticeResultWithAnalyticsController({ sessionData, selectedPresets })

    // const { setPracticeConfig, savePracticeSession } = usePracticeContext()

    const handleOnGoHomePressed = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.PRACTICE_MODULE.CLICKED_ON_GO_BACK, {
            [PAGE_NAME_KEY]: PAGE_NAMES.PRACTICE_RESULT,
        });
        router.replace('/nets')
    }, [router])

    const renderTabLabel = useCallback(({ route, focused, color }) => (
        <View style={{ borderBottomColor: dark.colors.secondary, borderBottomWidth: focused ? 2 : 0, paddingBottom: 10 }}>
            <Text style={{ ...styles.label, color: color }} numberOfLines={1}>
                {route.name}
            </Text>
        </View>
    ), []);

    const renderTabBar = useCallback((props) => {
        if (filteredAnalyticsData.length > 1) {
            return (
                <>
                    <TabBar
                        {...props}
                        style={styles.tabBar}
                        tabStyle={styles.tabStyle}
                        indicatorStyle={styles.indicator}
                        scrollEnabled={true}
                        labelStyle={styles.label}
                        activeColor={dark.colors.secondary}
                        inactiveColor={dark.colors.textDark}
                        renderLabel={renderTabLabel}
                    />
                    <View style={styles.fullWidthLine} />
                </>
            )
        }
        return null
    }, [filteredAnalyticsData])

    const renderScene = useCallback(({ route: analyticsInfo, jumpTo }) => {
        return (<ScrollView contentContainerStyle={{ paddingHorizontal: 16 }} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
            <ResultAnalyticsInfoWithConfigCard presetsAnalyticsInfo={analyticsInfo} />
        </ScrollView>)
    }, [])

    return (
        <View style={styles.container}>
            <Header />
            <View style={styles.innerContainer}>
                <View style={styles.analyticsContainer}>
                    <View style={styles.timeTakenWithQueButtonContainer}>
                        <View style={{ gap: 2 }}>
                            <Text style={styles.timeTakenText}>
                                {getFormattedTimeWithMS(totalTime * 1000).split(":")[0]} min {getFormattedTimeWithMS(totalTime * 1000).split(":")[1]} sec
                            </Text>
                            <Text style={styles.timeText}>
                                Total time taken
                            </Text>
                        </View>
                        {isCompactMode && (<TouchableOpacity style={{ flexDirection: "row", alignItems: "center", gap: 10 }} onPress={onPressViewQuestions}>
                            <Text style={{ fontFamily: "Montserrat-600", fontSize: 14, lineHeight: 20, color: dark.colors.secondary }}>
                                Questions
                            </Text>
                            <Icon
                                name="chevron-right"
                                type="font-awesome-5"
                                color={dark.colors.secondary}
                                size={13}
                            />
                        </TouchableOpacity>)}
                    </View>
                    <View style={{ flex: 1, width: '100%', justifyContent: 'flex-start', overflow: "scroll" }}>
                        <TabView
                            navigationState={{ index, routes: filteredAnalyticsData }}
                            renderScene={renderScene}
                            onIndexChange={onIndexChange}
                            initialLayout={initialLayout}
                            renderTabBar={renderTabBar}
                        />
                    </View>
                    {isCompactMode && (<View style={{ alignItems: "center", width: "100%" }}>
                        <TouchableOpacity
                            onPress={handlePlayAgainPressed}
                            style={styles.buttonStyle} >
                            <Text style={styles.buttonLabelStyle}>
                                Practice Again
                            </Text>
                        </TouchableOpacity>
                    </View>)}
                </View>
                {!isCompactMode && (
                    <View style={styles.queContainer}>
                        <QuestionView key={'question-view'} practiceSession={practiceSession} questionNo={`0`} selectedPresets={selectedPresets}/>
                    </View>
                )}
                {!isCompactMode && (<View style={{
                    width: "100%",
                    height: 'auto',
                    backgroundColor: dark.colors.background,
                    borderTopColor: dark.colors.tertiary,
                    borderTopWidth: 1,
                    paddingVertical: 10,
                    position: "absolute",
                    bottom: 0,
                    paddingHorizontal: 16,
                    flexDirection: 'row',
                    justifyContent: "space-between",
                    alignItems: "center"
                }}>
                    <TouchableOpacity
                        onPress={handleOnGoHomePressed}>
                        <Text style={styles.goBackLabelStyle}>
                            Go Back
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={handlePlayAgainPressed}
                        style={styles.buttonStyle} >
                        <Text style={styles.buttonLabelStyle}>
                            Practice Again
                        </Text>
                    </TouchableOpacity>
                </View>)}
            </View>
        </View>
    )
}

PracticeReultPage.propTypes = {
    practiceSession: PropTypes.object,
    practiceConfig: PropTypes.object
}

export default PracticeReultPage