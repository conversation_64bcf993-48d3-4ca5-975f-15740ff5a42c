import { FORM_INPUT_TYPES } from 'core/constants/forms'
import _get from 'lodash/get'
import _toNumber from 'lodash/toNumber'
import { EXPONENT_FIELDS_KEYS } from '../../constants/fieldKeys'

// eslint-disable-next-line import/prefer-default-export
export const getExponentFormFields = () => [
    {
        key: EXPONENT_FIELDS_KEYS.DIGITS_IN_BASE,
        type: FORM_INPUT_TYPES.NUMBER_INPUT,
        label: 'No of digits in base',
        required: true,
        visible: true,
        defaultValue: 2,
        rules: {
            minValue: 1,
            maxValue: 10,
        },
        additional: {
            incrementBy: 1,
        },
    },
    {
        key: EXPONENT_FIELDS_KEYS.MAX_EXPONENT,
        type: FORM_INPUT_TYPES.NUMBER_INPUT,
        label: 'Exponent value',
        required: true,
        visible: () => true,
        defaultValue: 2,
        rules: {
            minValue: 0,
            maxValue: 20,
        },
        additional: {
            incrementBy: 1,
        },
    },
    {
        key: EXPONENT_FIELDS_KEYS.DECIMALS_IN_EXPONENT,
        type: FORM_INPUT_TYPES.NUMBER_INPUT,
        label: 'No of Decimal digit in exponent',
        required: true,
        visible: () => true,
        defaultValue: 0,
        rules: {
            minValue: 0,
            maxValue: 10,
        },
        additional: {
            incrementBy: 1,
        },
    },
    {
        key: EXPONENT_FIELDS_KEYS.PRECISION_IN_ANSWER,
        type: FORM_INPUT_TYPES.NUMBER_INPUT,
        label: 'Precision upto n decimal place',
        required: true,
        visible: (formState) => _toNumber(_get(formState, [EXPONENT_FIELDS_KEYS.DECIMALS_IN_EXPONENT])) > 0,
        defaultValue: 0,
        rules: {
            minValue: 0,
            maxValue: 10,
        },
        additional: {
            incrementBy: 1,
        },
    },
    {
        key: EXPONENT_FIELDS_KEYS.NUMBER_OF_QUESTIONS,
        type: FORM_INPUT_TYPES.NUMBER_INPUT,
        label: 'No of Questions',
        required: true,
        visible: true,
        defaultValue: 10,
        rules: {
            minValue: 1,
            maxValue: 1000,
        },
        additional: {
            incrementBy: 1,
        },
    },
]
