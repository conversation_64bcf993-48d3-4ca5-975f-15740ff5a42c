import { FORM_INPUT_TYPES } from 'core/constants/forms'
import { MOD_FIELDS_KEYS } from '../../constants/fieldKeys'

// eslint-disable-next-line import/prefer-default-export
export const getModFormFields = () => [
    {
        key: MOD_FIELDS_KEYS.DIGITS_ROW_1,
        type: FORM_INPUT_TYPES.NUMBER_INPUT,
        label: 'Digits in Row 1',
        required: true,
        visible: true,
        defaultValue: 2,
        rules: {
            minValue: 2,
            maxValue: 10,
        },
        additional: {
            incrementBy: 1,
        },
    },
    {
        key: MOD_FIELDS_KEYS.DIGITS_ROW_2,
        type: FORM_INPUT_TYPES.NUMBER_INPUT,
        label: 'Digits in Row 2',
        required: true,
        visible: () => true,
        defaultValue: 2,
        rules: {
            minValue: 2,
            maxValue: 20,
        },
        additional: {
            incrementBy: 1,
        },
    },
    {
        key: MOD_FIELDS_KEYS.NUMBER_OF_QUESTIONS,
        type: FORM_INPUT_TYPES.NUMBER_INPUT,
        label: 'Questions',
        required: true,
        visible: true,
        defaultValue: 10,
        rules: {
            minValue: 1,
            maxValue: 1000,
        },
        additional: {
            incrementBy: 1,
        },
    },
]
