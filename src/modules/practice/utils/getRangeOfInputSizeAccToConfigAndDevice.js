import { PRACTICE_CATEGORIES } from "../pages/Practice/components/OperatorSelector/constants/practice";

const getRangeOfInputSizeAccToConfigAndDevice = ({ isCompactMode = true, operationTag }) => {

    // MAX VALUE IS :- No. of Digits Max Value 
    switch (operationTag) {
        case PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT:
            return isCompactMode ? { min: 1, max: 15 } : { min: 1, max: 20 };
        case PRACTICE_CATEGORIES.MULTIPLICATION:
            return isCompactMode ? { min: 1, max: 8 } : { min: 1, max: 20 }
        case PRACTICE_CATEGORIES.DIVISION:
            return isCompactMode ? { min: 1, max: 8 } : { min: 1, max: 20 }
    }

}

export default getRangeOfInputSizeAccToConfigAndDevice