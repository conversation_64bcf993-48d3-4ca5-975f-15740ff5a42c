const getTextStyleAccToConfig = ({ noOfRows = 10 }) => {
  if (noOfRows > 0 && noOfRows < 5) {
    return {
      fontSize: 20,
      gapBetweenRows: 24,
    };
  }
  if (noOfRows === 5 || noOfRows === 6) {
    return {
      fontSize: 16,
      gapBetweenRows: 16,
    };
  }
  if (noOfRows === 7 || noOfRows === 8) {
    return {
      fontSize: 14,
      gapBetweenRows: 16,
    };
  }

  return {
    fontSize: 14,
    gapBetweenRows: 12,
  };
};

export default getTextStyleAccToConfig;
