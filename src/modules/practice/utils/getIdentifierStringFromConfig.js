import _isEmpty from 'lodash/isEmpty';
import _split from 'lodash/split';
import { PRESET_CATEGORY } from 'core/constants/presets';
import _toNumber from 'lodash/toNumber';
import _round from 'lodash/round';
import _isNil from 'lodash/isNil';
import _compact from 'lodash/compact';
import _get from 'lodash/get';
import _times from 'lodash/times';
import _join from 'lodash/join';
import {
  EXPONENT_FIELDS_KEYS,
  MOD_FIELDS_KEYS,
  NTH_ROOT_FIELDS_KEYS,
} from '../constants/fieldKeys';
import { IDENTIFIER_FACTORY } from './getIdentifier';
import { PRACTICE_CATEGORIES } from '../pages/Practice/components/OperatorSelector/constants/practice';
import {
  DEFAULT_PRESET_CONFIG,
  FLASH_ANZAN_DEFAULT_CONFIG,
} from '../constants/presetConfig';

const getNthRootDescriptiveNameFromIdentifier = ({ identifier }) => {
  const [tag, digitsConfig] = _split(identifier, '_');
  const configValues = _split(digitsConfig, ',');

  const nthRoot = _toNumber(configValues[1]);
  const isPerfectPower = _toNumber(configValues[0]);
  const noOfDigits = _toNumber(configValues[2]);
  const roundOffDecimals = _toNumber(configValues[3]);

  let rootTag = `${nthRoot}N`;

  if (nthRoot === 2) {
    rootTag = `2N`;
  } else if (nthRoot === 3) {
    rootTag = `3N`;
  }

  let result = `${rootTag} ${noOfDigits}D`;
  if (!isPerfectPower) {
    result += ` ${roundOffDecimals}I`;
  } else {
    result += ` Perfect roots`;
  }

  return result;
};

const getNthRootTagsFromIdentifier = ({ identifier }) => {
  const [tag, digitsConfig] = _split(identifier, '_');

  const configValues = _split(digitsConfig, ',');

  const tags = [];

  const nthRoot = _toNumber(configValues[1]);
  const isPerfectPower = _toNumber(configValues[0]);
  const noOfDigits = _toNumber(configValues[2]);
  const roundOffDecimals = _toNumber(configValues[3]);

  if (nthRoot === 2) {
    tags.push('2nd Root');
  } else if (nthRoot === 3) {
    tags.push('3rd Root');
  } else {
    tags.push(`${nthRoot}th Root`);
  }

  tags.push(`${noOfDigits} Digits`);

  if (!isPerfectPower) {
    tags.push(`${roundOffDecimals} Decimal places`);
  } else {
    tags.push('Perfect roots');
  }

  return tags;
};

export const getConfigTagsFromIdentifier = ({ identifier }) => {
  const [tag, digitsConfig] = _split(identifier, '_');

  const configValues = _split(digitsConfig, ',');
  const noOfRows = configValues.length;

  if (noOfRows < 2) {
    return [];
  }

  switch (tag) {
    case PRESET_CATEGORY.ADD:
      return ['Addition', `${noOfRows} Rows`, `${configValues[0]} Digits`];
    case PRESET_CATEGORY.ADDSUB:
      return [
        'Addition',
        'Subtraction',
        `${noOfRows} Rows`,
        `${configValues[0]} Digits`,
      ];
    case PRESET_CATEGORY.MULT:
      return [
        'Multiplication',
        `${noOfRows} Rows`,
        `${configValues[0]} Digits R1`,
        `${configValues[1]} Digits R2`,
      ];
    case PRESET_CATEGORY.DIV:
      return [
        'Divison',
        `${noOfRows} Rows`,
        `${configValues[0]} Digits R1`,
        `${configValues[1]} Digits R2`,
      ];
    case PRESET_CATEGORY.FLASH_ANZAN:
      return [
        'Addition',
        `${configValues[0]} Digits`,
        `${configValues[1]}ms Speed`,
        `${configValues[2]} Numbers`,
      ];
    case PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB:
      return [
        'Addition',
        'Subtraction',
        `${configValues[0]} Digits`,
        `${configValues[1]}ms Speed`,
        `${configValues[2]} Numbers`,
      ];
    case PRESET_CATEGORY.ROOT:
    case PRESET_CATEGORY.NTH_ROOT:
      return getNthRootTagsFromIdentifier({ identifier });
    case PRESET_CATEGORY.EXPONENT:
      return _compact([
        'Exponent',
        `${configValues[0]} Digits Base`,
        `power is ${configValues[1]}${configValues[2] > 0 ? '.' : ''}${_join(
          _times(configValues[2], () => 'x'),
          '',
        )}`,
        !_isNil(configValues?.[3]) && `${configValues[3]} precision digits`,
      ]);
    case PRESET_CATEGORY.MOD:
      return [
        'Modulo',
        `${configValues[0]} Digits Dividend`,
        `${configValues[1]} Digits Divisor`,
      ];
  }
};

export const getIdentifierStringFromConfig = ({ config, tag }) => {
  const identifierGetter = IDENTIFIER_FACTORY[tag];

  return identifierGetter?.({ presetConfig: config }) ?? '';
};

export const getPresetConfigFromIdentifier = ({
  identifier,
  presetId,
  savedConfig,
}) => {
  const [tag, digitsConfig] = _split(identifier, '_');

  let config =
    tag !== PRESET_CATEGORY.FLASH_ANZAN &&
    tag !== PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB
      ? { ...DEFAULT_PRESET_CONFIG, noOfQuestions: 10, typingDirection: 'ltr' }
      : {
          ...FLASH_ANZAN_DEFAULT_CONFIG,
          noOfQuestions: 5,
        };

  const configValues = _split(digitsConfig, ',');

  let categoryId;

  const noOfRows = configValues.length;

  if (!_isEmpty(savedConfig)) {
    config = JSON.parse(savedConfig);
  }

  if (noOfRows < 2) {
    return {
      categoryId: PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT,
      config,
      id: presetId,
    };
  }

  const digitCount = parseInt(configValues[0]);

  if (tag === PRESET_CATEGORY.MULT) {
    config.noOfDigitsRow1 = parseInt(configValues[0]);
    config.noOfDigitsRow2 = parseInt(configValues[1]);
    categoryId = PRACTICE_CATEGORIES.MULTIPLICATION;
    return {
      categoryId,
      config,
      id: presetId,
    };
  }

  if (tag === PRESET_CATEGORY.DIV) {
    config.noOfDigitsRow1 = parseInt(configValues[0]);
    config.noOfDigitsRow2 = parseInt(configValues[1]);
    categoryId = PRACTICE_CATEGORIES.DIVISION;
    return {
      categoryId,
      config,
      id: presetId,
    };
  }

  if (tag === PRESET_CATEGORY.ADDSUB) {
    config.rows = noOfRows;
    config.digits = digitCount;
    config.includeSubstraction = true;
    categoryId = PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT;
    return {
      categoryId,
      config,
      id: presetId,
    };
  }

  if (tag === PRESET_CATEGORY.ADD) {
    config.rows = noOfRows;
    config.digits = digitCount;
    categoryId = PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT;
    return {
      categoryId,
      config,
      id: presetId,
    };
  }

  if (tag === PRESET_CATEGORY.FLASH_ANZAN) {
    config.digits = parseInt(configValues[0]);
    config.includeSubstraction = false;
    config.flashSpeed = parseInt(configValues[1]);
    config.numberCount = parseInt(configValues[2]);
    return { config };
  }

  if (tag === PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB) {
    config.includeSubstraction = true;
    config.digits = parseInt(configValues[0]);
    config.flashSpeed = parseInt(configValues[1]);
    config.numberCount = parseInt(configValues[2]);
    return { config };
  }

  if (tag === PRESET_CATEGORY.ROOT || tag === PRESET_CATEGORY.NTH_ROOT) {
    const nthRoot = _toNumber(configValues[1]);
    const isPerfectPower = _toNumber(configValues[0]);
    const noOfDigits = _toNumber(configValues[2]);
    const roundOffDecimals = _toNumber(configValues[3]);

    config[NTH_ROOT_FIELDS_KEYS.NO_OF_DIGITS] = noOfDigits;
    config[NTH_ROOT_FIELDS_KEYS.ROOT] = nthRoot;
    config[NTH_ROOT_FIELDS_KEYS.IS_PERFECT_POWER] = isPerfectPower;
    config[NTH_ROOT_FIELDS_KEYS.ROUND_OFF_TO_N_DECIMALS] = roundOffDecimals;

    return {
      categoryId: PRACTICE_CATEGORIES.ROOT,
      config,
      id: presetId,
    };
  }

  if (tag === PRESET_CATEGORY.EXPONENT) {
    const digitsInBase = _toNumber(configValues[0]);
    const maxExponent = _toNumber(configValues[1]);
    const decimalsInExponent = _toNumber(configValues[2]);

    config[EXPONENT_FIELDS_KEYS.DIGITS_IN_BASE] = digitsInBase;
    config[EXPONENT_FIELDS_KEYS.MAX_EXPONENT] = maxExponent;
    config[EXPONENT_FIELDS_KEYS.DECIMALS_IN_EXPONENT] = decimalsInExponent;
    config[EXPONENT_FIELDS_KEYS.PRECISION_IN_ANSWER] = _toNumber(
      _get(configValues, '3'),
    );

    return {
      categoryId: PRACTICE_CATEGORIES.EXPONENT,
      config,
      id: presetId,
    };
  }

  if (tag === PRESET_CATEGORY.MOD) {
    let config = { ...DEFAULT_PRESET_CONFIG, noOfQuestions: 10 };

    if (!_isEmpty(savedConfig)) {
      config = JSON.parse(savedConfig);
    }

    const configValues = _split(digitsConfig, ',');
    config[MOD_FIELDS_KEYS.DIGITS_ROW_1] = parseInt(configValues[0]);
    config[MOD_FIELDS_KEYS.DIGITS_ROW_2] = parseInt(configValues[1]);

    return {
      categoryId: PRACTICE_CATEGORIES.MOD,
      config,
      id: presetId,
    };
  }
};

export const getCategoryIdFromIdentifier = ({ identifier }) => {
  const [tag, digitsConfig] = _split(identifier, '_');

  if (tag === PRESET_CATEGORY.MULT) {
    return PRACTICE_CATEGORIES.MULTIPLICATION;
  }

  if (tag === PRESET_CATEGORY.DIV) {
    return PRACTICE_CATEGORIES.DIVISION;
  }

  if (tag === PRESET_CATEGORY.ADDSUB) {
    return PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT;
  }

  if (tag === PRESET_CATEGORY.ADD) {
    return PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT;
  }

  if (
    tag === PRESET_CATEGORY.FLASH_ANZAN ||
    tag === PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB
  ) {
    return PRACTICE_CATEGORIES.FLASH_ANZAN;
  }

  if (tag === PRESET_CATEGORY.ROOT || tag === PRESET_CATEGORY.NTH_ROOT) {
    return PRACTICE_CATEGORIES.ROOT;
  }

  if (tag === PRESET_CATEGORY.EXPONENT) {
    return PRACTICE_CATEGORIES.EXPONENT;
  }

  if (tag === PRESET_CATEGORY.MOD) {
    return PRACTICE_CATEGORIES.MOD;
  }

  return '';
};

export const getTagStringFromIdentifier = ({ identifier }) => {
  const [tag, digitsConfig] = _split(identifier, '_');

  if (tag === PRESET_CATEGORY.ADD || tag === PRESET_CATEGORY.ADDSUB) {
    return PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT;
  }
  if (
    tag === PRESET_CATEGORY.FLASH_ANZAN ||
    tag === PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB
  ) {
    return PRACTICE_CATEGORIES.FLASH_ANZAN;
  }
  if (tag === PRESET_CATEGORY.MULT) {
    return PRACTICE_CATEGORIES.MULTIPLICATION;
  }
  if (tag === PRESET_CATEGORY.DIV) {
    return PRACTICE_CATEGORIES.DIVISION;
  }
  if (tag === PRESET_CATEGORY.ROOT || tag === PRESET_CATEGORY.NTH_ROOT) {
    return PRACTICE_CATEGORIES.ROOT;
  }

  if (tag === PRESET_CATEGORY.EXPONENT) {
    return PRACTICE_CATEGORIES.EXPONENT;
  }

  if (tag === PRESET_CATEGORY.MOD) {
    return PRACTICE_CATEGORIES.MOD;
  }

  return '';
};

const getExponentDescriptiveNameFromIdentifier = ({ identifier }) => {
  const [tag, digitsConfig] = _split(identifier, '_');
  const configValues = _split(digitsConfig, ',');

  const digitsInBase = configValues[0];
  const maxExponent = configValues[1];
  const decimalsInExponent = configValues[2];

  return `${digitsInBase}D Base, ${maxExponent}N, ${decimalsInExponent} I`;
};

const getModDescriptiveNameFromIdentifier = ({ identifier }) => {
  const [tag, digitsConfig] = _split(identifier, '_');
  const configValues = _split(digitsConfig, ',');

  return `${configValues[0]}D ÷ ${configValues[1]}D Mod`;
};

export const getDescriptiveNameFromIdentifier = ({ identifier }) => {
  const [tag, digitsConfig] = _split(identifier, '_');
  const configValues = _split(digitsConfig, ',');
  const noOfRows = configValues.length;

  if (noOfRows < 2) {
    return identifier;
  }

  switch (tag) {
    case PRESET_CATEGORY.ADD:
      return `${configValues[0]}D, ${noOfRows}R Add`;
    case PRESET_CATEGORY.ADDSUB:
      return `${configValues[0]}D, ${noOfRows}R Add/Sub`;
    case PRESET_CATEGORY.MULT:
      return `${configValues[0]}D x ${configValues[1]}D`;
    case PRESET_CATEGORY.DIV:
      return `${configValues[0]}D ÷ ${configValues[1]}D`;
    case PRESET_CATEGORY.FLASH_ANZAN:
      return `${configValues[0]}D,${_round(configValues[1] / 1000, 2)}s,${configValues[2]}N Add`;
    case PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB:
      return `${configValues[0]}D,${_round(configValues[1] / 1000, 2)}s,${configValues[2]}N Add/Sub`;
    case PRESET_CATEGORY.ROOT:
    case PRESET_CATEGORY.NTH_ROOT:
      return getNthRootDescriptiveNameFromIdentifier({ identifier });
    case PRESET_CATEGORY.EXPONENT:
      return getExponentDescriptiveNameFromIdentifier({ identifier });
    case PRESET_CATEGORY.MOD:
      return getModDescriptiveNameFromIdentifier({ identifier });
    default:
      return identifier;
  }
};
