import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      width: isCompactMode ? '100%' : '30%',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
    },
    playersContainer: {
      width: '100%',
      height: 150,
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    versusContainerStyle: {
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
      position: 'relative',
    },
    versusText: {
      fontSize: 32,
      fontFamily: 'Montserrat-800',
      letterSpacing: 2,
      color: dark.colors.secondary,
      alignSelf: 'center',
      textAlign: 'center',
    },
    gameModeSelectorContainer: {
      flexDirection: 'row',
      width: '100%',
      height: 120,
      justifyContent: 'space-evenly',
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: 12,
    },
    buttonStyle: {
      borderWidth: 0.8,
      borderColor: dark.colors.tertiary,
      borderRadius: 8,
      height: 36,
    },
    buttonContainerStyle: {
      width: 170,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 12,
      height: 50,
    },
    buttonTextStyle: {
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      color: dark.colors.textLight,
    },
    buttonBorderBackgroundStyle: {
      width: 170,
      backgroundColor: dark.colors.tertiary,
      height: 20,
      bottom: 2,
    },
    footer: {
      width: '100%',
      height: 82,
      paddingHorizontal: 8,
      paddingVertical: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    playNowButton: {
      borderWidth: 0.8,
      borderColor: dark.colors.secondary,
      borderRadius: 10,
      height: 50,
    },
    playNowButtonContainer: {
      flex: 1,
      width: '100%',
      minWidth: 200,
      justifyContent: 'center',
      alignItems: 'center',
      maxWidth: 400,
      height: 50,
      borderRadius: 12,
    },
    playNowButtonText: {
      fontSize: 12,
      fontFamily: 'Montserrat-800',
      lineHeight: 17,
      color: dark.colors.textLight,
    },
    playNowBackground: {
      flex: 1,
      backgroundColor: dark.colors.victoryColor,
      borderColor: dark.colors.secondary,
      borderWidth: 1,
    },
  });

const useCreateLobbyStyles = () => {
  const { isMobile } = useMediaQuery();
  const styles = useMemo(() => createStyles(isMobile), [isMobile]);
  return styles;
};

export default useCreateLobbyStyles;
