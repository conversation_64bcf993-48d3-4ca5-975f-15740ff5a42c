import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import React, { useCallback } from 'react';

import { ScrollView, View } from 'react-native';
import GameTypeCard from '@/src/components/shared/GameTypeCard';
import userReader from '@/src/core/readers/userReader';
import { GAME_TYPE_DETAILS } from '@/src/modules/home/<USER>/gameTypes';
import { GAME_TYPES } from '@/src/core/constants/gameTypes';
import { useRouter } from 'expo-router';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import Analytics from '@/src/core/analytics';
import { PAGE_NAMES } from '@/src/core/constants/pageNames';
import FriendsListTabView from '@/src/modules/friendsAndFollowers/components/FriendsListTabView';
import UserRatingCardWithBgLines from '@/src/components/UserRatingCardWithBgLines';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import { GAME_MODE, modeRatingLabels } from '../../constants/gameModes';
import styles from './GameModesSubSection.styles';

const GameModeSubSection = (props) => {
  const { user } = useSession();
  const { selectedMode, gameTypes } = props;
  const ratingLabel = modeRatingLabels[selectedMode] || 'Rating';
  const router = useRouter();

  let userRating = userReader.rating(user);
  switch (selectedMode) {
    case GAME_MODE.CLASSICAL:
      userRating = userReader.abilityDuelsRating(user);
      break;
    case GAME_MODE.MEMORY:
      userRating = userReader.flashAnzanRating(user);
      break;
  }

  const handleGameTypeSelect = useCallback(
    (gameType) => {
      if (
        gameType === GAME_TYPES.FLASH_ANZAN ||
        gameType === GAME_TYPES.GROUP_PLAY
      ) {
        router.push(`/play-time/instructions?gameType=${gameType}`);
        return;
      }
      if (gameType === GAME_TYPES.MOST_PLAYED) {
        router.push(`/search?timeLimit=1&gameType=${GAME_TYPES.PLAY_ONLINE}`);
        return;
      }
      router.push(`/play-time?gameType=${gameType}`);
    },
    [router],
  );

  const onPressGameType = useCallback((gameType) => {
    let eventName = '';
    switch (gameType) {
      case GAME_TYPES.MOST_PLAYED:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_MOST_PLAYED;
        break;
      case GAME_TYPES.PLAY_ONLINE:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_ONLINE_DUELS;
        break;
      case GAME_TYPES.PLAY_WITH_FRIEND:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_FRIEND;
        break;
      case GAME_TYPES.PRACTICE:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_PRACTICE;
        break;
      case GAME_TYPES.FLASH_ANZAN:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_FLASH_ANZAN;
        break;
      case GAME_TYPES.FASTEST_FINGER:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_FASTEST_FINGER;
        break;
      case GAME_TYPES.GROUP_PLAY:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_GROUP_PLAY;
        break;
      default:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_ONLINE_DUELS;
    }
    Analytics.track(eventName, {
      pageName: PAGE_NAMES.ARENA_PAGE,
    });
    handleGameTypeSelect(gameType);
  }, []);

  const RenderGameTypeCards = () => {
    if (_isEmpty(gameTypes)) {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            // minHeight: 400,
          }}
        >
          <Rive
            url={RIVE_ANIMATIONS.COMING_SOON}
            autoPlay
            style={{ height: 240, width: 257, resizeMode: 'contain' }}
          />
        </View>
      );
    }
    return (
      <View style={styles.gameTypesContainer}>
        {_map(gameTypes, (gameType) => {
          const gameTypeDetails = GAME_TYPE_DETAILS[gameType];
          const { title, subtitle, tags, gradientColor } = gameTypeDetails;
          return (
            <GameTypeCard
              title={title}
              subtitle={subtitle}
              tags={tags}
              gradientColor={gradientColor}
              onPress={() => onPressGameType(gameType)}
            />
          );
        })}
      </View>
    );
  };

  if (selectedMode === GAME_MODE.VS_FRIEND) {
    return <FriendsListTabView />;
  }

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <UserRatingCardWithBgLines
          ratingLabel={ratingLabel}
          userRating={userRating}
        />
        <RenderGameTypeCards />
      </ScrollView>
    </View>
  );
};

export default React.memo(GameModeSubSection);
