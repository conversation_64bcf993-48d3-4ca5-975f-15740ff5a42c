/* eslint-disable arrow-body-style */
import React, { useMemo } from 'react';
import { View, Text } from 'react-native';
import _get from 'lodash/get';
import FallbackImage from '@/src/components/atoms/helpers/fallbackImage';
import Logo from 'assets/images/notificationCentre/logo.png';
import styles from './FeedCard.style';
import SideImage from '../SideImage';
import CardFooter from '../CardFooter';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';

// create a func to get date into 5 min ago 10 min ago 10 day ago like this
const getFormattedDate = (date: string) => {
  const now = new Date();
  const notificationDate = new Date(date);
  const timeDiff = now.getTime() - notificationDate.getTime();
  const seconds = Math.floor(timeDiff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  if (seconds < 60) {
    return `${seconds} sec ago`;
  }
  if (minutes < 60) {
    return `${minutes} min ago`;
  }
  if (hours < 24) {
    return `${hours} hr ago`;
  }
  return `${days} day ago`;
};

const FeedCard = ({
  feedId,
  feedType,
  feed,
  imageUrl,
  isLiked,
}: {
  feedId: string;
  feedType: string;
  feed: any;
  imageUrl: string;
  isLiked: boolean;
}) => {
  const { userId } = useSession();
  const { title, description } = useMemo(() => {
    if (userId === feed?.sentFor) {
      return {
        title: _get(feed, ['title']),
        description: _get(feed, ['description']),
      };
    }

    return {
      title: _get(feed, ['feedForFriends', 'title']),
      description: _get(feed, ['feedForFriends', 'body']),
    };
  }, [userId, feed]);
  return (
    <View style={[styles.container]}>
      <View style={styles.mainContainer}>
        <View style={styles.contentContainer}>
          <View style={styles.imageContainer}>
            <FallbackImage
              source={{
                uri: imageUrl,
              }}
              fallbackSource={Logo}
              style={styles.image}
            />
          </View>
          <View style={styles.content}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.time}>
                {getFormattedDate(_get(feed, ['sentAt']))}
              </Text>
            </View>
            <Text style={styles.description}>{description}</Text>
          </View>
        </View>
        <SideImage type={feedType} />
      </View>
      <View style={styles.buttonContainer}>
        <CardFooter
          feedType={feedType}
          isLiked={isLiked}
          feedId={feedId}
          likesCount={_get(feed, ['likesCount'])}
        />
      </View>
    </View>
  );
};

export default React.memo(FeedCard);
