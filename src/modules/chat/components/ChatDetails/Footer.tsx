import React, { useCallback, useRef, useState } from 'react';
import { Image, TextInput, TouchableOpacity, View } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import sendImage from '@/assets/images/message/send.png';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import styles from './ChatDetails.style';

const Footer = ({
  sendMessage: sendMessageFromProps,
  containerStyle = {},
  disabled,
}: any) => {
  const inputRef = useRef(null);
  const [message, setMessage] = useState('');

  const { isMobile: isCompactMode } = useMediaQuery();

  const sendMessage = useCallback(() => {
    sendMessageFromProps?.(message);
    setMessage('');
  }, [sendMessageFromProps, message]);

  const handleKeyPress = useCallback(
    (e: any) => {
      if (isCompactMode) {
        return;
      }
      if (e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
        sendMessage();
        e.preventDefault();
      }
    },
    [sendMessage, isCompactMode],
  );

  return (
    <View style={[styles.footer, containerStyle]}>
      <View style={styles.inputContainer}>
        <TextInput
          ref={inputRef}
          style={styles.input}
          placeholder="Write your message..."
          placeholderTextColor={Dark.colors.tertiary}
          onChangeText={(text) => {
            setMessage(text);
          }}
          value={message}
          multiline
          maxLength={250}
          onKeyPress={handleKeyPress}
        />
        <TouchableOpacity
          style={[styles.sendButton, disabled && styles?.sendButtonDisabled]}
          disabled={disabled}
          onPress={sendMessage}
        >
          <View style={styles.sendButtonGradient}>
            <Image source={sendImage} style={styles.sendButtonImage} />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default React.memo(Footer);
