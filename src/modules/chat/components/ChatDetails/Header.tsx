import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Icon } from '@rneui/themed';
import Dark from '@/src/core/constants/themes/dark';
import { Group } from '../../types/groups';
import groupReader from '../../readers/groupReader';
import styles from './ChatDetails.style';

const Header = ({
  group,
  onBackPress,
}: {
  group: Group;
  onBackPress: () => void;
}) => {
  const user = groupReader.individual(group);
  const avatar = user?.profileImageUrl;
  const name = user?.name;

  return (
    <View style={styles.header}>
      <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
        <Icon
          name="chevron-left"
          type="font-awesome-5"
          color={Dark.colors.textLight}
          size={16}
        />
      </TouchableOpacity>

      <View style={styles.headerProfile}>
        <View style={styles.headerAvatarContainer}>
          <Image source={{ uri: avatar }} style={styles.headerAvatar} />
        </View>
        <View style={styles.headerInfo}>
          <Text style={styles.headerName}>{name}</Text>
        </View>
      </View>
    </View>
  );
};

export default React.memo(Header);
