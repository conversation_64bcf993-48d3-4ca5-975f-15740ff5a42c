import { StyleSheet, Text, TouchableOpacity } from 'react-native';
import React from 'react';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { useRouter } from 'expo-router';
import { useCallback } from 'react';
import userReader from 'core/readers/userReader';
import dark from 'core/constants/themes/dark';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import EmptyComponent from 'shared/EmptyComponent';

const EmptyScreen = () => {
  const { user } = useSession();
  const router = useRouter();
  const handleAddFriends = useCallback(() => {
    router.push(`/profile/${userReader.username(user)}/friends`);
  }, [router, user]);

  const ButtonComponent = useCallback(() => {
    return (
      <TouchableOpacity
        style={styles.addFriendsButton}
        onPress={handleAddFriends}
      >
        <MaterialIcons name="add" size={16} color={dark.colors.secondary} />
        <Text style={styles.addFriendsButtonText}>Add friends</Text>
      </TouchableOpacity>
    );
  }, [handleAddFriends]);

  return (
    <EmptyComponent
      ButtonComponent={ButtonComponent}
      title="No Chats"
      subTitle="Please checkout the friends section to lookout friends to chat"
    />
  );
};

export default React.memo(EmptyScreen);

const styles = StyleSheet.create({
  addFriendsButton: {
    marginTop: 20,
    width: 140,
    height: 32,
    borderWidth: 1,
    borderColor: dark.colors.primary,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  addFriendsButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
  },
});
