import React from "react"
import { View, Text, TextInput } from "react-native"
import styles from "../../EditProfile.style"
import dark from "../../../../../../core/constants/themes/dark"
import PropTypes from "prop-types"
import _isEmpty from "lodash/isEmpty"

const TextInputWithLabel = (props) => {
    const { label, onChangeText, value, placeholderText, isRequired = true, error ,multiline=false,numberOfLines=1, renderRightAccessories } = props

    return (
        <View style={styles.inputContainer}>
            <View style={{ flexDirection: 'row', gap: 3, }}>
                <Text style={styles.inputLabel}>{label}</Text>
                {isRequired && (<Text style={styles.requiredTag}>*</Text>)}
            </View>
            <View style={styles.inputBoxContainer}>
                <TextInput
                    style={styles.input}
                    placeholder={placeholderText}
                    placeholderTextColor={dark.colors.placeholder}
                    value={value}
                    onChangeText={onChangeText}
                    // multiline={multiline}
                    numberOfLines={numberOfLines}
                />
                {renderRightAccessories?.()}
            </View>

            {!_isEmpty(error) && (<Text style={styles.errorLabel}>
                {error}
            </Text>)}
        </View>
    )
}

TextInputWithLabel.propTypes = {
    label: PropTypes.string,
    isRequired: PropTypes.bool,
    onChangeText: PropTypes.func,
    value: PropTypes.string,
    placeholderText: PropTypes.string,
    error: PropTypes.string,
    multiline: PropTypes.bool,
    numberOfLines: PropTypes.number
}

export default React.memo(TextInputWithLabel)