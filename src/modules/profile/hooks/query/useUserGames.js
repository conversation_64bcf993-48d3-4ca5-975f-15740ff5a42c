import useGetGamesByUserQuery from './useGetGamesByUserQuery'
import { useMemo, useState, useCallback } from 'react'
import _map from 'lodash/map'
import _find from 'lodash/find'
import _compact from 'lodash/compact'
import _keyBy from 'lodash/keyBy'
import _keys from 'lodash/keys'
import _sortBy from 'lodash/sortBy'
import _reverse from "lodash/reverse";

const useUserGames = ({ user, pageSize = 10 }) => {
    const userId = user?._id
    const [games, setGames] = useState([])
    const [users, setUsers] = useState([])
    const [pageNumber, setPageNumber] = useState(0)

    const { loading, error, fetchRecentGames } = useGetGamesByUserQuery({
        userId,
        pageSize,
    })

    const normalizedUsers = useMemo(() => _keyBy(users, '_id'), [users]);

    const processGames = useCallback((games, users) => {
        const normalizedUsers = _keyBy(users, '_id');
        const sortedGames = _reverse(_sortBy(games, 'startTime'));

        return _compact(
            _map(sortedGames, (game) => {
                const playersMap = _keyBy(game?.players, 'userId')
                const leaderboardMap = _keyBy(game?.leaderBoard, 'userId')

                const currentPlayer = {
                    ...normalizedUsers[userId],
                    ...playersMap[userId],
                    ...leaderboardMap[userId],
                }

                const opponentUserId = _find(
                    _keys(playersMap),
                    (key) => key !== userId
                )

                const opponentUser = {
                    ...normalizedUsers[opponentUserId],
                    ...playersMap[opponentUserId],
                    ...leaderboardMap[opponentUserId],
                }

                let gameResult
                if (currentPlayer.rank === 1) {
                    gameResult = 'win'
                } else if (opponentUser.rank === 1) {
                    gameResult = 'lose'
                } else {
                    gameResult = 'draw'
                }

                return {
                    ...game,
                    currentPlayer,
                    opponentUser,
                    gameResult
                }
            })
        )
    }, [userId]);

    const fetchPageGames = useCallback(async ({ pageNumber }) => {
        setPageNumber(pageNumber)
        const result = await fetchRecentGames({ pageNumber })
        if (result?.data?.getGamesByUser) {
            const { games, users } = result.data.getGamesByUser;
            setGames(games)
            setUsers(users)
            const recentGames = processGames(games, users);
            const statsData = _reverse(_map(
                recentGames,
                ({ currentPlayer }) => currentPlayer.rating + currentPlayer.ratingChange
            ));
            return {
                recentGames,
                statsData,
            }
        }
        return EMPTY_OBJECT;
    }, [fetchRecentGames, processGames])

    return {
        fetchPageGames,
        loading,
        error,
        currentPage: pageNumber,
    }
}

export default useUserGames