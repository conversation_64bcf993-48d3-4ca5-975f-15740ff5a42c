import { useMutation, gql } from '@apollo/client'
import { useCallback } from 'react'

import _isEmpty from "lodash/isNil";
import { CURRENT_USER_FRAGMENT } from 'core/graphql/fragments/user';


const UPDATE_USER_QUERY = gql`
    ${CURRENT_USER_FRAGMENT}
    mutation UpdateUser($updateUserInput: UpdateUserInput){
        updateUser(updateUserInput: $updateUserInput){
            ...CurrentUserFields
        }
    }
`

const useUpdateUserProfile = () => {
    const [updatedUserQuery] = useMutation(UPDATE_USER_QUERY);

    const updateUser = useCallback((userInput) => {
        if (_isEmpty(userInput)) {
            return null;
        }
        const response = updatedUserQuery({ variables: { updateUserInput: userInput } })

        return response
    }, [updatedUserQuery]);

    return {
        updateUser
    }
}

export default useUpdateUserProfile;