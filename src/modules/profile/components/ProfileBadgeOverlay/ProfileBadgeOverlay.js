import { Overlay } from "@rneui/base"
import React, {useCallback, useMemo, useRef, useState} from "react"
import Entypo from '@expo/vector-icons/Entypo';
import { View, Text, Dimensions, Image } from "react-native"
import dark from "../../../../core/constants/themes/dark.js";
import BadgesList from "../../constants/badges.js";
import useProfileBadgeOverlayStyles from './ProfileBadgeOverlay.style.js'
import PropTypes from "prop-types";
import Carousel from "react-native-reanimated-carousel";
import _findIndex from "lodash/findIndex";
import _isEmpty from "lodash/isEmpty";
import _toUpper from "lodash/toUpper";
import useUserPercentile from "../../../../overlays/hooks/useUserPercentile";
import _isNil from "lodash/isNil";

const { width, height } = Dimensions.get('window');

const ProfileBadgeOverlay = (props) => {
    const styles = useProfileBadgeOverlayStyles()
    const { onClose, isVisible, badge: userBadge } = props;
    const carouselRef = useRef(null)
    const [activeIndex, setActiveIndex] = useState(0)

    const userPercentile = useUserPercentile();

    const onProgressChange = useCallback((offsetProgress, absoluteProgress) => {
        const currentIndex = Math.round(absoluteProgress);
        if (currentIndex !== activeIndex) {
            setActiveIndex(currentIndex);
        }
    }, [activeIndex, setActiveIndex]);

    const onChange = useCallback((currentIndex) => {
        setActiveIndex(currentIndex)
    }, [setActiveIndex])

    const userBadgeIndex = useMemo(() => {
        const userBadgeIndex = _findIndex(BadgesList, badge => badge.key === userBadge.key);
        return userBadgeIndex === -1 ? 0 : userBadgeIndex;
    }, [userBadge]);

    const renderItem = useCallback(({ item, index }) => {
        const isActive = index === activeIndex
        const isHigherBadges = index > userBadgeIndex;
        const title = _toUpper(index === userBadgeIndex ? `YOU ARE A ${item.title}` : item.title);
        const currentBadgeDescription = !_isNil(userPercentile) && userPercentile < 50 ? `You are among top ${userPercentile}% mathletes on Matiks` : 'You are doing great, Keep playing on Matiks';
        const description = index === userBadgeIndex ? currentBadgeDescription : index > userBadgeIndex ? item?.howToEarnDescription : item?.earnedDescription
        const titleColor = isHigherBadges ? dark.colors.textDark : index < userBadgeIndex ? 'white' : item?.color;

        return (
            <View style={{ justifyContent: "center", width: 206, height: 206, alignItems: "center" }}>
                <View style={[styles.imageContainer, !isActive && { justifyContent: 'flex-start', alignItems: 'center', marginTop: 30 }]}>
                    <Image source={item.image} style={[isActive ? { width: 206, height: 206 } : styles.image, isHigherBadges && { opacity: 0.3 }]} />
                </View>
                {isActive && (<View style={styles.textContainer}>
                    <Text style={[styles.titleText, { color: titleColor }]}>{title}</Text>
                    <Text style={styles.descriptionText}>{description}</Text>
                </View>)}
            </View>
        )
    }, [activeIndex, userBadgeIndex]);

    return (
        <Overlay overlayStyle={styles.overlayContainer} isVisible={isVisible} onBackdropPress={onClose}>
            <View style={styles.headerRow}>
                <Entypo name="cross" size={22} color={dark.colors.textDark} onPress={onClose} />
            </View>
            <Carousel
                ref={carouselRef}
                loop={false}
                width={206}
                height={206}
                defaultIndex={userBadgeIndex ?? 0}
                data={BadgesList}
                scrollAnimationDuration={2000}
                style={{ width: width * 0.9, height: 250, alignItems: 'center', justifyContent: 'center', overflow: 'hidden' }}
                renderItem={renderItem}
                mode={"parallax"}
                modeConfig={{
                    parallaxScrollingScale: 0.96,
                    parallaxScrollingOffset: 80,
                }}
                onProgressChange={onProgressChange}
                autoPlayInterval={2000}
            />
        </Overlay>
    )
}

ProfileBadgeOverlay.propTypes = {
    onClose: PropTypes.func,
    isVisible: PropTypes.bool
}

export default React.memo(ProfileBadgeOverlay)