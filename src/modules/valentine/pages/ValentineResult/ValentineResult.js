import React from 'react';
import {
  Dimensions,
  Image,
  Linking,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';

import useMediaQuery from 'core/hooks/useMediaQuery';
import babaGif from '@/assets/images/kateki-baba-ji.gif';
import tweet1 from '@/assets/images/twitter/tweet1.png';
import tweet2 from '@/assets/images/twitter/tweet2.png';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import SocialChannelsFooter from '@/src/components/shared/SocialChannelsFooter';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import dark from 'core/constants/themes/dark';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import { useRouter } from 'expo-router';
import Analytics from 'core/analytics';

const ValentineResult = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const router = useRouter();
  const { handleShare: handleShareUrl } = useNativeUrlSharing({
    url: 'https://valentine.matiks.com/valentine',
  });
  const space = isCompactMode ? 24 : 48;
  const width = Math.min(620, Dimensions.get('window').width - 32);

  const [tweet1Width, tweet1Height] = (() => {
    const tweet1Width = Math.min(500, width);
    const tweet1Height = (160 / 500) * tweet1Width;
    return [tweet1Width, tweet1Height];
  })();

  const [tweet2Width, tweet2Height] = (() => {
    const tweet2Width = Math.min(590, width);
    const tweet2Height = (275 / 590) * tweet2Width;
    return [tweet2Width, tweet2Height];
  })();

  const handleShare = () => {
    Analytics.track('Valentine: Clicked on Share With Your Friends');
    handleShareUrl({
      label: 'Find Valentine date on instagram',
      clipboardLabel: 'Copied url to clipboard',
    });
  };

  const goToHomePage = () => {
    Analytics.track('Valentine: Clicked on Get Some Math Rizz');
    if (Platform.OS === 'web') {
      window.location.href = 'https://www.matiks.com/apps';
    } else {
      Linking.openURL('https://www.matiks.com/apps');
    }
  };

  return (
    <ScrollView>
      <View
        style={{
          flex: 1,
          alignItems: 'center',
          gap: space,
          padding: space,
          paddingHorizontal: 16,
        }}
      >
        <View
          style={{
            borderWidth: 1,
            borderColor: 'white',
            padding: 3,
            borderRadius: 4,
            marginTop: isCompactMode ? 16 : 32,
          }}
        >
          <Image
            source={babaGif}
            style={{ width: 300, height: 300, borderRadius: 2 }}
          />
        </View>
        <View style={{ alignItems: 'center', gap: 24 }}>
          <Text
            style={{
              color: 'white',
              fontSize: 24,
              fontFamily: 'Montserrat-600',
              textAlign: 'center',
            }}
          >
            People who are good in math are more likely to get Date
          </Text>
          <InteractivePrimaryButton
            label="Get Some Math Rizz"
            buttonContainerStyle={{ width: 200 }}
            onPress={goToHomePage}
          />
          <Text
            style={{
              color: 'white',
              fontSize: 14,
              fontFamily: 'Montserrat-500',
              textAlign: 'center',
            }}
          >
            Don't agree?, better listen to the girls 👇🏻
          </Text>
        </View>
        <View
          style={{
            flexDirection: 'column',
            gap: 16,
            alignItems: 'center',
            justifyContent: 'center',
            flexWrap: 'wrap',
          }}
        >
          <Image
            source={tweet1}
            style={{
              width: tweet1Width,
              height: tweet1Height,
              borderRadius: 8,
            }}
          />
          <Image
            source={tweet2}
            style={{
              width: tweet2Width,
              height: tweet2Height,
              borderRadius: 8,
            }}
          />
        </View>
        <Pressable
          onPress={handleShare}
          style={{
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 20,
            borderWidth: 1,
            borderColor: dark.colors.secondary,
            marginTop: 32,
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Text style={{ color: dark.colors.secondary, textAlign: 'center' }}>
              Share With Your Friends
            </Text>
            <FontAwesome5
              name="share-alt"
              size={12}
              color={dark.colors.secondary}
            />
          </View>
        </Pressable>
      </View>
      <SocialChannelsFooter />
    </ScrollView>
  );
};

export default React.memo(ValentineResult);
