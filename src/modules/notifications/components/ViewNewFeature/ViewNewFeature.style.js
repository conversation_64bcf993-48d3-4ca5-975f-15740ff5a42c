import { useMemo } from 'react'
import { StyleSheet } from 'react-native'
import useMediaQuery from 'core/hooks/useMediaQuery'

const createStyles = () =>
    StyleSheet.create({
        container: {},
    })

const useViewNewFeatureNotificationCardStyles = () => {
    const { isMobile: isCompactMode } = useMediaQuery()

    return useMemo(() => createStyles(isCompactMode), [isCompactMode])
}

export default useViewNewFeatureNotificationCardStyles
