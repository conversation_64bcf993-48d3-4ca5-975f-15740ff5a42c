import { getDefaultPuzzleDate } from 'modules/puzzles/utils/puzzleUtils';
import useGetDailyPuzzle from 'modules/puzzles/hooks/queries/useGetDailyPuzzle';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';

const useCrossMathPuzzleResultPageController = ({
  puzzleDate = getDefaultPuzzleDate(),
}: {
  puzzleDate?: string;
} = EMPTY_OBJECT) => {
  const { puzzle, loading, error } = useGetDailyPuzzle({
    date: puzzleDate,
    fetchPolicy: 'network-only',
    puzzleType: PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  });

  return {
    puzzle,
    loading,
    error,
  };
};

export default useCrossMathPuzzleResultPageController;
