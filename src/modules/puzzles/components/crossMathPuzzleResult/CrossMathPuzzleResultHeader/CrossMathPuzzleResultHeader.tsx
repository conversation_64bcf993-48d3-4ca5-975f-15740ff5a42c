import React, { useCallback } from 'react';
import { Platform, Text, View } from 'react-native';
import BackToHomeButton from 'molecules/BackToHomeButton';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import { PUZZLE_TYPES, PuzzleType } from 'modules/puzzles/types/puzzleType';
import { PUZZLE_TYPES_VS_LABEL } from 'modules/puzzles/constants/puzzleConstants';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import puzzleResultReader from 'modules/puzzles/readers/puzzleResultReader';
import { getFormattedTimeObject } from 'core/utils/general';
import { format, parse } from 'date-fns';
import { openShareableCardFlow } from 'shared/ShareResultModal';
import CrossMathPuzzleResultInfoCard from 'modules/puzzles/components/crossMathPuzzleResult/CrossMathPuzzleResultInfoCard';
import { useLocalSearchParams } from 'expo-router';

const formatDateToHuman = (dateString: string): string => {
  const date = parse(dateString, 'yyyy-MM-dd', new Date());

  return format(date, 'do MMM');
};

const CrossMathPuzzleResultHeader = ({ puzzle }: { puzzle: PuzzleType }) => {
  const puzzleDate = puzzleReader.puzzleDate(puzzle);
  const puzzleUrl = `https://www.matiks.com/puzzle/daily-challenge/${puzzleDate}`;

  const userResult = puzzleReader.currentUserResult(puzzle);

  const { puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE } =
    useLocalSearchParams();

  const timeSpent = puzzleResultReader.timeSpent(userResult);

  const timeObject = getFormattedTimeObject(timeSpent);

  const { handleShare } = useNativeUrlSharing({ url: puzzleUrl });

  const renderPuzzleResultCard = useCallback(
    () => (
      <View style={{ flexShrink: 1, height: 250, width: 275 }}>
        <CrossMathPuzzleResultInfoCard
          puzzle={puzzle}
          puzzleType={puzzleType}
        />
      </View>
    ),
    [puzzle],
  );

  const puzzleTypeLabel = `${PUZZLE_TYPES_VS_LABEL[puzzleType]} Puzzle`;

  const onPressShareButton = useCallback(() => {
    const { hours, minutes, seconds } = timeObject;
    const humanReadableDate = formatDateToHuman(puzzleDate);
    const timeSpentString = `${hours > 0 ? `${hours} hours ` : ''}${minutes > 0 ? `${minutes} minutes ` : ''}${seconds} seconds`;
    const label = `I have solved Matiks ${humanReadableDate} ${puzzleTypeLabel} 🧩 in ${timeSpentString} 🥳`;

    if (Platform.OS === 'web') {
      handleShare({
        label,
        clipboardLabel: 'Copied Puzzle link to clipboard',
      });
    } else {
      openShareableCardFlow({
        renderResultCard: renderPuzzleResultCard,
        message: label,
        storyBackgroundColors: {
          backgroundBottomColor:
            dark.colors.puzzle.share.storyBackgroundColorBottom,
          backgroundTopColor: dark.colors.puzzle.share.storyBackgroundColorTop,
        },
      });
    }
  }, [
    handleShare,
    timeObject,
    puzzleDate,
    renderPuzzleResultCard,
    puzzleTypeLabel,
  ]);

  return (
    <View
      style={{
        width: '100%',
        flexDirection: 'row',
        gap: 8,
        padding: 16,
        maxWidth: 600,
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <BackToHomeButton />
      <Text
        style={{ color: 'white', fontSize: 12, fontFamily: 'Montserrat-600' }}
      >
        DID YOU EVEN SWEAT?
      </Text>
      <InteractiveSecondaryButton
        onPress={onPressShareButton}
        iconConfig={{
          name: 'share',
          type: ICON_TYPES.ENTYPO,
          color: dark.colors.secondaryButtonBorder,
          size: 20,
        }}
        buttonContainerStyle={{ width: 36, height: 36 }}
      />
    </View>
  );
};

export default React.memo(CrossMathPuzzleResultHeader);
