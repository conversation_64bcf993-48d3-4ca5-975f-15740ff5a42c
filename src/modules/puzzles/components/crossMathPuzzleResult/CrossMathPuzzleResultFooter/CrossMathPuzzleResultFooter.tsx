import React from 'react';
import { View } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import PuzzleDateSelectorModal from 'modules/puzzles/components/PuzzleDateSelector/PuzzleDateSelectorModal';
import dark from 'core/constants/themes/dark';
import MetricStatCard from 'shared/MetricStatCard/MetricStatCard';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import puzzleResultReader from 'modules/puzzles/readers/puzzleResultReader';
import StaticCoinGainedMetric from 'shared/StaticCoinGainedMetric';
import puzzleStatsReader from 'modules/puzzles/readers/puzzleUserReader';
import { getFormattedTimeWithMS } from 'core/utils/general';
import { withOpacity } from 'core/utils/colorUtils';

const CrossMathPuzzleResultFooter = ({ puzzle, date }: { puzzle: any }) => {
  const puzzleResult = puzzleReader.currentUserResult(puzzle);
  const coinsGained = puzzleResultReader.statikCoinsEarned(puzzleResult);

  const currentPuzzleTimeTaken = puzzleResultReader.timeSpent(puzzleResult);

  const userStat = puzzleReader.userStat(puzzle);
  const bestTime = puzzleStatsReader.bestTime(userStat);

  const isNewBestTime = currentPuzzleTimeTaken === bestTime;

  const formattedBestTime = getFormattedTimeWithMS(bestTime);

  return (
    <View
      style={{
        padding: 16,
        gap: 20,
        paddingBottom: 24,
        maxWidth: 400,
        alignSelf: 'center',
        width: '100%',
      }}
    >
      <View style={{ flexDirection: 'row', gap: 13, width: '100%' }}>
        <View style={{ flex: 1 }}>
          <MetricStatCard
            label={isNewBestTime ? 'NEW BEST TIME' : 'BEST TIME'}
            value={formattedBestTime}
            backgroundColor={
              isNewBestTime
                ? withOpacity(dark.colors.puzzle.primary, 0.4)
                : dark.colors.tertiary
            }
          />
        </View>
        <View style={{ flex: 1 }}>
          <StaticCoinGainedMetric coinsGained={coinsGained} />
        </View>
      </View>
      <View style={{ flexDirection: 'row', gap: 8 }}>
        <PuzzleDateSelectorModal date={date} shouldCleanStack>
          {({ onPressPuzzleDateTab }) => (
            <View style={{ width: '100%' }}>
              <InteractiveSecondaryButton
                label="PLAY MORE"
                labelStyle={{ fontSize: 12 }}
                onPress={onPressPuzzleDateTab}
                borderColor={dark.colors.puzzle.primary}
              />
            </View>
          )}
        </PuzzleDateSelectorModal>
      </View>
    </View>
  );
};

export default React.memo(CrossMathPuzzleResultFooter);
