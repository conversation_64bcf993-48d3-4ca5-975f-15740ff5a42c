import React, { useCallback } from 'react';
import { Pressable, Text } from 'react-native';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import { PUZZLE_DETAILS } from 'modules/puzzles/constants/puzzleConstants';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import Analytics from '@/src/core/analytics';
import { PAGE_NAME_KEY, PAGE_NAMES } from '@/src/core/constants/pageNames';
import { router } from 'expo-router';
import styles from './PuzzleDCTypeCard.style';

const PuzzleDCTypeCard = ({ type }: { type: string }) => {
  const { animationUrl, puzzleTypeText } = PUZZLE_DETAILS[type];

  const onPressPuzzleTypeCard = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.CLICKED_ON_SOLVE_CROSS_MATH_PUZZLE,
      {
        [PAGE_NAME_KEY]: PAGE_NAMES.PUZZLE_HOME_PAGE,
        puzzleType: type,
      },
    );
    router.push(`/puzzle/daily-challenge?puzzleType=${type}`);
  }, [type]);

  return (
    <Pressable style={styles.container} onPress={onPressPuzzleTypeCard}>
      <Rive
        url={animationUrl ?? RIVE_ANIMATIONS.PUZZLE_ANIMATION}
        autoPlay
        style={{ width: 125, height: 140 }}
      />
      <Text style={styles.puzzleTypeText}>{puzzleTypeText}</Text>
    </Pressable>
  );
};

export default React.memo(PuzzleDCTypeCard);
