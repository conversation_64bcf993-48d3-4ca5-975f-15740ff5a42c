import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    borderColor: dark.colors.puzzle.primary,
    borderWidth: 1,
    paddingHorizontal: 16,
    borderRadius: 20,
    justifyContent: 'space-between',
    maxHeight: 220,
    paddingVertical: 14,
    gap: 5,
    flexDirection: 'column',
    alignItems: 'center',
  },
  puzzleTypeText: {
    fontSize: 16,
    fontFamily: 'Montserrat-500',
    color: 'white',
  },
});

export default styles;
