import { useLocalSearchParams } from 'expo-router';
import React, { useEffect } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { View } from 'react-native';
import ErrorView from 'atoms/ErrorView';
import { checkIsValidDate } from 'modules/puzzles/utils/puzzleUtils';
import KenKenPuzzleHeader from 'modules/puzzles/components/KenKenPuzzleHeader';
import KenKenPuzzle from './KenKenPuzzle';

const TRACKED_ATTEMPT_FOR_DATE = {};

const KenKenPuzzleContainer = () => {
  const { date } = useLocalSearchParams();

  const isValidDate = checkIsValidDate(date);

  useEffect(() => {
    if (TRACKED_ATTEMPT_FOR_DATE[date]) return;
    TRACKED_ATTEMPT_FOR_DATE[date] = true;
    Analytics.track(ANALYTICS_EVENTS.KEN_KEN_PUZZLE.ATTEMPTING_KEN_KEN_PUZZLE, {
      date,
    });
  }, [date]);

  if (!isValidDate) {
    return (
      <View style={{ flex: 1 }}>
        <KenKenPuzzleHeader />
        <ErrorView errorMessage="Sorry, Puzzle is not available for selected date" />
      </View>
    );
  }

  return <KenKenPuzzle date={date} />;
};

export default React.memo(KenKenPuzzleContainer);
