import { UserResultType } from '../types/puzzleType';

const puzzleResultReader = {
  id: (puzzleResult: UserResultType): string => puzzleResult?.id,
  userId: (puzzleResult: UserResultType): string => puzzleResult?.userId,
  puzzleId: (puzzleResult: UserResultType): string => puzzleResult?.puzzleId,
  timeSpent: (puzzleResult: UserResultType | null): number | undefined =>
    puzzleResult?.timeSpent,
  completedAt: (puzzleResult: UserResultType): number =>
    puzzleResult?.completedAt,
  puzzleDate: (puzzleResult: UserResultType): string =>
    puzzleResult?.puzzleDate,
  statikCoinsEarned: (puzzleResult: UserResultType): number =>
    puzzleResult?.statikCoinsEarned,
};

export default puzzleResultReader;
