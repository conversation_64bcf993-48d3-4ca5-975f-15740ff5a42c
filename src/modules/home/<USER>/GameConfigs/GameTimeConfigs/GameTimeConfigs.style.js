import { StyleSheet } from 'react-native'
import Dark from '@/src/core/constants/themes/dark'
import dark from '../../../../../core/constants/themes/dark'

const styles = StyleSheet.create({
    container: {
        gap: 12,
    },

    gameConfigContainer: {
        gap: 6,
        justifyContent: "center",
    },
    configTitle: {
        color: Dark.colors.textDark,
        fontSize: 16,
        marginBottom: 6,
    },
    configOptionsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 12,
        width: '100%',
        height: 50,
    },
    buttonContainer: {
        width: '45%',
        maxWidth: 160,
    },
    compactButtonContainer: {
        maxWidth: 120,
    },

    //V2 Stylings 
    title: {
        fontSize: 12,
        fontFamily: 'Montserrat-700',
        color: 'white'
    },
    subTitle:{
        fontSize: 12, 
        fontFamily: "Montserrat-500", 
        color: dark.colors.textDark 
    },
    timeContainer :{
        flex:1,
        backgroundColor:'transparent',
        borderColor:dark.colors.tertiary,
        borderWidth:0.5,
        height: 30,
        maxWidth: 200,  
        minWidth: '43%',
        overflow: 'hidden'
    },

    buttonLabelStyle :{
        fontSize:12,
        letterSpacing: -0.5,
        fontFamily:'Montserrat-600',
        color: dark.colors.secondary
    }
})

export default styles
