import React, { useCallback, useMemo, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import UserImage from 'atoms/UserImage';
import PropTypes from 'prop-types';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import { closePopover } from 'molecules/Popover/Popover';
import ChallengeIcon from 'assets/images/duel.png';
import FlashAnzanIcon from '@/assets/images/flashAnzan.png';
import FastestFingerIcon from '@/assets/images/fastestFinger.png';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useChallengeUserForPuzzleGame from 'modules/puzzleGame/hooks/mutations/useChallengeUserForPuzzleGame';
import PuzzleIcon from '@/src/components/svg/Icons/PuzzleIcon';
import useChallengeUser from '../../../friendsAndFollowers/hooks/mutations/useChallengeUser';
import { GAME_TYPES } from '../../constants/gameTypes';
import userReader from '../../../../core/readers/userReader';
import USER_ACTIVITY from '../../../../core/constants/userActivityConstants';
import dark from '../../../../core/constants/themes/dark';
import useOnlineUserBottomSheetStyles from './OnlineUserBottomSheet.style';

const OnlineUserBottomSheet = (props) => {
  const { user, currActivity } = props;
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const styles = useOnlineUserBottomSheetStyles();

  const { name, rating, _id: currentUserId } = user ?? EMPTY_OBJECT;

  const { challengeUser } = useChallengeUser();

  const { challengeUserForPuzzleGame } = useChallengeUserForPuzzleGame();

  const [isChallengingFriend, setIsChallengingFriend] = useState(false);

  const onPressQuickOneMinChallenge = useCallback(async () => {
    try {
      if (isChallengingFriend) {
        return;
      }
      setIsChallengingFriend(true);
      await challengeUser({ userId: currentUserId });
      closePopover?.();
      setIsChallengingFriend(false);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [challengeUser, currentUserId, isChallengingFriend]);

  const onPressPuzzleChallenge = useCallback(async () => {
    try {
      if (isChallengingFriend) {
        return;
      }
      setIsChallengingFriend(true);
      await challengeUserForPuzzleGame({ userId: currentUserId });
      closePopover?.();
      setIsChallengingFriend(false);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [challengeUserForPuzzleGame, currentUserId, isChallengingFriend]);

  const onPressFlashAnzanChallenge = useCallback(async () => {
    try {
      if (isChallengingFriend) {
        return;
      }
      setIsChallengingFriend(true);

      await challengeUser({
        userId: currentUserId,
        gameConfig: {
          timeLimit: 95,
          numPlayers: 2,
          gameType: GAME_TYPES.FLASH_ANZAN,
        },
      });
      closePopover?.();
      setIsChallengingFriend(false);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [challengeUser, currentUserId, isChallengingFriend]);

  const onPressCustomChallenge = useCallback(() => {
    closePopover?.();
    router.push(`games/lobby?friendId=${userReader.id(user)}`);
  }, [user, router]);

  const onPressPlayFastestFinger = useCallback(async () => {
    closePopover?.();
    try {
      setIsChallengingFriend(true);
      const gameConfig = {
        timeLimit: 60,
        numPlayers: 2,
        gameType: GAME_TYPES.FASTEST_FINGER,
      };
      await challengeUser({ userId: currentUserId, gameConfig });
    } catch (e) {
      console.error('Error sending challenge:', e);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [user, router]);

  const onPressViewProfile = useCallback(() => {
    closePopover?.();
    router.push(`profile/${userReader.username(user)}`);
  }, [router, user]);

  const isExploring = useMemo(
    () => currActivity === USER_ACTIVITY.EXPLORING,
    [currActivity],
  );

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom + 8 }]}>
      <View style={styles.userInfoRow}>
        <View style={styles.userImageWithIndicator}>
          <UserImage user={user} rounded={false} style={styles.userImage} />
          <View
            style={[
              styles.activityIndicator,
              isExploring && { backgroundColor: dark.colors.secondary },
            ]}
          />
        </View>
        <View style={styles.userInfoContainer}>
          <Text style={styles.name} numberOfLines={1}>
            {userReader.username(user)}
          </Text>
          <Text style={styles.userRating}>{rating}</Text>
          <View
            style={[
              styles.activityContainer,
              isExploring && {
                backgroundColor: dark.colors.secondary,
                width: 52,
              },
            ]}
          >
            <Text style={styles.activityText} numberOfLines={1}>
              {isExploring ? 'Online' : currActivity}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.buttonOptionsContainer}>
        {isExploring && (
          <TouchableOpacity
            style={styles.buttonRowContainer}
            onPress={onPressQuickOneMinChallenge}
          >
            <Image source={ChallengeIcon} style={{ height: 15, width: 15 }} />
            <Text style={[styles.buttonText, { color: dark.colors.secondary }]}>
              Quick 1-min challenge
            </Text>
          </TouchableOpacity>
        )}

        {isExploring && (
          <TouchableOpacity
            style={styles.buttonRowContainer}
            onPress={onPressPuzzleChallenge}
          >
            <PuzzleIcon color={dark.colors.secondary} height={15} width={15} />
            <Text style={[styles.buttonText]}>Cross Math Puzzle(2 MIN)</Text>
          </TouchableOpacity>
        )}
        {isExploring && (
          <TouchableOpacity
            style={styles.buttonRowContainer}
            onPress={onPressPlayFastestFinger}
          >
            <Image
              source={FastestFingerIcon}
              style={{ height: 18, width: 18 }}
            />
            <Text style={styles.buttonText}>Fastest Finger</Text>
          </TouchableOpacity>
        )}

        {isExploring && (
          <TouchableOpacity
            style={styles.buttonRowContainer}
            onPress={onPressFlashAnzanChallenge}
          >
            <Image source={FlashAnzanIcon} style={{ height: 15, width: 15 }} />
            <Text style={styles.buttonText}>Flash Anzan challenge</Text>
          </TouchableOpacity>
        )}

        {isExploring && (
          <TouchableOpacity
            style={styles.buttonRowContainer}
            onPress={onPressCustomChallenge}
          >
            <Ionicons name="options-outline" size={15} color="white" />
            <Text style={styles.buttonText}>Custom challenge</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.buttonRowContainer}
          onPress={onPressViewProfile}
        >
          <Ionicons name="person-circle-outline" size={18} color="white" />
          <Text style={styles.buttonText}>View profile</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

OnlineUserBottomSheet.propTypes = {
  user: PropTypes.object,
  currActivity: PropTypes.string,
};

export default React.memo(OnlineUserBottomSheet);
