import React, { useCallback, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useRouter } from 'expo-router';
import { closePopover, showPopover } from 'molecules/Popover/Popover';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import PropTypes from 'prop-types';
import useChallengeUserForPuzzleGame from 'modules/puzzleGame/hooks/mutations/useChallengeUserForPuzzleGame';
import CustomChallengePopover from '../../../game/components/CustomChallengePopover';
import styles from './OnlineUsersOptionsOverlay.style';
import useChallengeUser from '../../../friendsAndFollowers/hooks/mutations/useChallengeUser';
import { GAME_TYPES } from '../../constants/gameTypes';
import dark from '../../../../core/constants/themes/dark';

const OnlineUsersOptionsOverlay = (props) => {
  const { user, isExploring } = props;
  const { _id: currentUserId } = user ?? EMPTY_OBJECT;
  const router = useRouter();
  const [isChallengingFriend, setIsChallengingFriend] = useState(false);
  const { challengeUser } = useChallengeUser();
  const { challengeUserForPuzzleGame } = useChallengeUserForPuzzleGame();

  const onPressCustomChallenge = useCallback(() => {
    if (!isExploring) {
      return;
    }
    showPopover({
      content: <CustomChallengePopover opponentUser={user} />,
      style: styles.customChallengePopover,
      overlayLook: true,
    });
  }, [user, isExploring]);

  const onPressPlayFastestFinger = useCallback(() => {
    if (!isExploring) {
      return;
    }
    showPopover({
      content: (
        <CustomChallengePopover
          opponentUser={user}
          gameType={GAME_TYPES.FASTEST_FINGER}
        />
      ),
      style: styles.customChallengePopover,
      overlayLook: true,
    });
  }, [user, isExploring]);

  const onPressPuzzleChallenge = useCallback(async () => {
    if (!isExploring) {
      return;
    }
    try {
      if (isChallengingFriend) {
        return;
      }
      setIsChallengingFriend(true);
      await challengeUserForPuzzleGame({ userId: currentUserId });
      closePopover?.();
      setIsChallengingFriend(false);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [
    challengeUserForPuzzleGame,
    currentUserId,
    isChallengingFriend,
    isExploring,
  ]);

  const onPressFlashAnzanChallenge = useCallback(async () => {
    if (!isExploring) {
      return;
    }

    try {
      if (isChallengingFriend) {
        return;
      }
      setIsChallengingFriend(true);

      await challengeUser({
        userId: currentUserId,
        gameConfig: {
          timeLimit: 95,
          numPlayers: 2,
          gameType: GAME_TYPES.FLASH_ANZAN,
        },
      });
      closePopover?.();
      closeRightPane?.();
      setIsChallengingFriend(false);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [challengeUser, currentUserId, isChallengingFriend, isExploring]);

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={onPressCustomChallenge}>
        <Text
          style={[
            styles.buttonText,
            !isExploring && { color: dark.colors.textDark },
          ]}
        >
          Custom Challenge
        </Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={onPressPuzzleChallenge}>
        <Text
          style={[
            styles.buttonText,
            !isExploring && { color: dark.colors.textDark },
          ]}
        >
          Cross Math Puzzle(2 MIN)
        </Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={onPressPlayFastestFinger}>
        <Text
          style={[
            styles.buttonText,
            !isExploring && { color: dark.colors.textDark },
          ]}
        >
          Fastest Finger
        </Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={onPressFlashAnzanChallenge}>
        <Text
          style={[
            styles.buttonText,
            !isExploring && { color: dark.colors.textDark },
          ]}
        >
          FlashAnzan Challenge
        </Text>
      </TouchableOpacity>
    </View>
  );
};

OnlineUsersOptionsOverlay.propTypes = {
  user: PropTypes.object,
  isExploring: PropTypes.bool,
};

export default React.memo(OnlineUsersOptionsOverlay);
