import { Dimensions, StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from 'core/constants/themes/dark';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    mainContainer: {
      gap: 12,
      paddingHorizontal: 0,
      width: Dimensions.get('window').width,
      paddingTop: isCompactMode ? 12 : 0,
    },
    headerContainer: {
      justifyContent: 'space-between',
      alignItems: 'center',
      flexDirection: 'row',
      paddingHorizontal: 12,
    },
    container: {
      gap: 24,
      marginLeft: 12,
    },
    onlinePeopleText: {
      letterSpacing: 1,
      fontSize: 10,
      lineHeight: 12,
      fontFamily: 'Montserrat-600',
      color: dark.colors.textDark,
      opacity: 0.5,
    },
    noUsers: {
      fontSize: 10,
      lineHeight: 13,
      fontFamily: 'Montserrat-500',
      color: dark.colors.textDark,
    },
    noUsersContainer: {
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

const useCompactOnlineUsersSectionStyles = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const styles = useMemo(() => createStyles(isCompactMode), [isCompactMode]);
  return styles;
};

export default useCompactOnlineUsersSectionStyles;
