import React from 'react';
import { ScrollView, View } from 'react-native';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import useMediaQuery from 'core/hooks/useMediaQuery';
import OnlineUserCard from '../../OnlineUserCard';
import useCompactOnlineUsersSectionStyles from './CompactOnlineUsersSection.style';
import OnlineUserShimmerCard from '../../OnlineUserCard/OnlineUserShimmerCard';

const CompactOnlineUsersSection = (props) => {
  const { onlineUsers, navigateToOnlineUsersPage, isFetchingOnlineUsers } =
    props ?? EMPTY_OBJECT;

  const { isMobile: isCompactMode } = useMediaQuery();

  const styles = useCompactOnlineUsersSectionStyles();

  const renderOnlineUsersShimmer = () => (
    <ScrollView
      contentContainerStyle={styles.container}
      horizontal
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
    >
      {_map(Array.from({ length: 6 }), (_, index) => (
        <OnlineUserShimmerCard key={`online-user-shimmer-${index}`} />
      ))}
    </ScrollView>
  );

  // const renderEmptyUsersPlaceholder = () =>
  //   !isCompactMode ? null : (
  //     <Text style={styles.noUsers}>No Mathletes Online</Text>
  //   );

  const renderOnlineUsers = () => (
    <ScrollView
      contentContainerStyle={styles.container}
      horizontal
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
    >
      {_map(onlineUsers, (userData, index) => (
        <OnlineUserCard
          user={userData?.userInfo}
          currActivity={userData?.currActivity}
          key={`online-user-${index}`}
        />
      ))}
      <OnlineUserCard />
    </ScrollView>
  );

  return (
    <View style={styles.mainContainer}>
      {isFetchingOnlineUsers
        ? renderOnlineUsersShimmer()
        : _isEmpty(onlineUsers)
          ? null
          : renderOnlineUsers()}
    </View>
  );
};

CompactOnlineUsersSection.propTypes = {
  onlineUsers: PropTypes.array,
  navigateToOnlineUsersPage: PropTypes.func,
  isFetchingOnlineUsers: PropTypes.bool,
};

export default React.memo(CompactOnlineUsersSection);
