import { StyleSheet, Dimensions } from "react-native"
import dark from "core/constants/themes/dark"

const styles = StyleSheet.create({
    container: {
        alignItems: "center",
        width: "100%",
        paddingHorizontal: 16
    },
    streakRow: {
        flexDirection: "row",
        width: "100%",
        marginBottom: 10,
        justifyContent: "space-between"
    },
    streakItem: {
        width: 50,
        height: 50,
        borderRadius: 15,
        backgroundColor: dark.colors.cardBackground,
        justifyContent: "center",
        alignItems: "center",
        borderWidth: 1,
        borderColor: dark.colors.cardBackground,
        marginHorizontal: 5,
    },
    todayHighlight: {
        borderColor: dark.colors.secondary,
        backgroundColor: dark.colors.primary
    },
    streakText: {
        color: "white",
        fontSize: 12,
        fontFamily: "Montserrat-500",
        marginTop: 5,
    },
    icon: {
        width: 20,
        height: 20,
    },
    messageContainer: {
        position: "relative",
        alignItems: "center",
    },
    triangle: {
        position: "absolute",
        top:-4,
        width: 0,
        height: 0,
        borderLeftWidth: 10,
        borderRightWidth: 10,
        borderBottomWidth: 10,
        borderLeftColor: "transparent",
        borderRightColor: "transparent",
        borderBottomColor: dark.colors.secondary,
    },
    outerTriangle: {
        position: "absolute",
        top: -8,
        width: 0,
        height: 0,
        borderLeftWidth: 10,
        borderRightWidth: 10,
        borderBottomWidth: 10,
        borderLeftColor: "transparent",
        borderRightColor: "transparent",
        borderBottomColor: "#2D542E"
    },
    outerMessageBox :{
        backgroundColor: '#2D542E', 
        paddingVertical: 3,
        paddingHorizontal:4, 
        borderRadius: 8, 
        elevation: 5 
    },
    messageBox: {
        flexDirection: "row",
        backgroundColor: dark.colors.secondary,
        borderRadius: 6,
        paddingVertical: 4,
        paddingHorizontal: 12,
        alignItems: "center",
        justifyContent: "space-between",
        width: Dimensions.get("window").width - 32,
    },
    messageText: {
        color: dark.colors.background,
        fontSize: 12,
        fontFamily: "Montserrat-600",
        flex: 1,
        marginRight: 10,
    },
    sparkle: {
        color: "#FFD700",
    },
    arrowContainer: {
        backgroundColor: dark.colors.darkGreen,
        borderRadius: 18,
        height: 30,
        width: 30,
        justifyContent: "center",
        alignItems: "center",
        padding: 8,
    },
    arrow: {
        textAlign: "center",
        color: "white",
        fontSize: 16,
    },
});

export default styles