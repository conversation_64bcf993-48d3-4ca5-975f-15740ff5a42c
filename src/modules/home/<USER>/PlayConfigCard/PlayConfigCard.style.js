import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        paddingLeft: 12,
        paddingRight: 26,
        paddingVertical: 10,
        backgroundColor: dark.colors.primary,
        borderWidth: 1,
        borderColor: dark.colors.tertiary,
        flexDirection:"row",
        alignItems:"center",
        gap:10,
        maxWidth: 300,
        width: 300,
    },
    infoText:{
        fontSize: 10, 
        fontFamily: "Montserrat-500", 
        lineHeight: 12, 
        color: dark.colors.textDark, 
        maxWidth: 180
    },
    titleText:{
        fontSize: 12, 
        fontFamily: "Montserrat-700", 
        lineHeight: 13, 
        color: "white" ,
        letterSpacing:1
    }
})

export default styles