import React from 'react';
import { Platform, Text, View } from 'react-native';
import LinearGradientText from 'atoms/LinearGradientText';
import dark from 'core/constants/themes/dark';
import useStreakAnalytics from 'modules/profile/hooks/query/useStreakAnalytics';
import { Square } from 'tamagui';
import styles from './EarnYourStreakOverlayCard.style';

const EarnYourStreakOverlayCard = ({
  showEarnStreak,
}: {
  showEarnStreak: boolean;
}) => {
  const { hasStreak } = useStreakAnalytics();

  if (hasStreak || !showEarnStreak) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Square
        enterStyle={{
          scale: 1.1,
          y: -10,
          opacity: 0,
        }}
        animation="bouncy"
        elevation="$4"
        opacity={1}
        scale={1}
        y={0}
        height={40}
        justifyContent="center"
      >
        <View style={styles.triangle} />
        <View style={styles.messageBox}>
          <LinearGradientText
            textStyle={styles.earnStreakText}
            colors={dark.colors.earnStreakText}
          >
            {Platform.OS !== 'web' ? (
              <Text style={styles.earnStreakText}>EARN TODAY’S STREAK</Text>
            ) : (
              'EARN TODAY’S STREAK'
            )}
          </LinearGradientText>
        </View>
      </Square>
    </View>
  );
};

export default React.memo(EarnYourStreakOverlayCard);
