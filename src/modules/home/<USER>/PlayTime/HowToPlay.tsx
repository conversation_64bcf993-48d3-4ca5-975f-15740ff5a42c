import { Text, XStack, YStack } from 'tamagui';
import { Image, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import HowToPlayGif from 'assets/images/game/how_to_play.gif';
import _map from 'lodash/map';
import React from 'react';

const howToPlayContent = [
  {
    content: 'Compete in real-time against a \nmathlete of similar rating. ',
  },
  {
    content:
      'Solve as many arithmetic problems \nas possible within the time limit.',
  },
  {
    content: 'Compete in real-time against a \nmathlete of similar rating. ',
  },
];

const HowToPlay = () => (
  <View style={{ gap: 5, padding: 16 }}>
    <XStack justifyContent="center" alignItems="center">
      <Text color="#FFF" fontSize={28} fontFamily="Montserrat-900">
        How To Play ?
      </Text>
    </XStack>
    <YStack space="$3" padding="$2" justifyContent="center" alignItems="center">
      <XStack
        borderWidth={1}
        borderColor={dark.colors.secondary}
        borderRadius={12}
      >
        <Image
          source={HowToPlayGif}
          style={{ width: 260, height: 300, borderRadius: 2 }}
        />
      </XStack>
      {_map(howToPlayContent, (content, index) => (
        <View
          style={{
            paddingHorizontal: 16,
            paddingVertical: 12,
            width: 260,
            borderWidth: 1,
            borderColor: dark.colors.secondary,
            borderRadius: 8,
            marginVertical: 10,
          }}
        >
          <Text
            style={{
              fontFamily: 'Montserrat-500',
              fontSize: 12,
              textAlign: 'center',
            }}
          >
            {content.content}
          </Text>
        </View>
      ))}
    </YStack>
  </View>
);

export default React.memo(HowToPlay);
