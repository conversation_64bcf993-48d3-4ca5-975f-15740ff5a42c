import blitzIcon from '@/assets/images/icons/blitz.png';
import classicIcon from '@/assets/images/icons/classic.png';
import flashAnzanIcon from '@/assets/images/icons/flash_anzan.png';
import userReader from "core/readers/userReader";

export const LEADERBOARD_TYPES = {
  FRIENDS: 'FRIENDS',
  GLOBAL: 'GLOBAL'
}

export const RATING_TYPES = {
  GLOBAL: 'BLITZ',
  MEMORY: 'MEMORY',
  ABILITY: 'ABILITY'
}

export const RATING_KEYS = {
  GLOBAL: 'globalRating',
  MEMORY: 'memoryRating',
  ABILITY: 'abilityRating'
}

const LEADERBOARD_CATEGORIES = [
  {
    title: RATING_TYPES.GLOBAL,
    icon: blitzIcon,
    ratingKey: RATING_KEYS.GLOBAL
  },
  {
    title: RATING_TYPES.ABILITY,
    icon: classicIcon,
    ratingKey: RATING_KEYS.ABILITY
  },
  {
    title: RATING_TYPES.MEMORY,
    icon: flashAnzanIcon,
    ratingKey: RATING_KEYS.MEMORY
  }
];

export const getRatingTypeFromTitle = (title) => {
  switch (title) {
    case RATING_TYPES.GLOBAL:
      return RATING_KEYS.GLOBAL;
    case RATING_TYPES.ABILITY:
      return RATING_KEYS.ABILITY;
    case RATING_TYPES.MEMORY:
      return RATING_KEYS.MEMORY;
    default:
      return RATING_KEYS.GLOBAL;
  }
};

export const getRatingForTitle = (title, user) => {
  switch (title) {
    case RATING_TYPES.GLOBAL:
      return userReader.rating(user);
    case RATING_TYPES.MEMORY:
      return userReader.flashAnzanRating(user);
    case RATING_TYPES.ABILITY:
      return userReader.abilityDuelsRating(user);
    default:
      return userReader.rating(user);
  }
};


export const TAB_INDICES = {
  FRIENDS: 0,
  GLOBAL: 1,
};

export const TABS = [
  {label: LEADERBOARD_TYPES.FRIENDS, iconName: 'people'},
  {label: LEADERBOARD_TYPES.GLOBAL, iconName: 'public'},
];

export default LEADERBOARD_CATEGORIES;