import { StyleSheet } from 'react-native'
import Dark from '@/src/core/constants/themes/dark'
import APP_LAYOUT_CONSTANTS from '../../../../core/constants/appLayout'

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    marginEnd: 16,
  },
  expandedContainer: {
    paddingHorizontal: 36,
    paddingTop: 24,
  },
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
  },
  userListStyle: {
    paddingHorizontal: 24,
  },
  heading: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    marginBottom: 16,
  },
  item: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  loadingIndicator: {
    marginTop: 50,
  },
  textContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    color: 'white',
  },

  headerContainer: {
    marginHorizontal: 24,
  },
  headerLabelStyle: {
    color: Dark.colors.textDark,
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    lineHeight: 20,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  separator: {
    width: '100%',
    height: 1,
    // minHeight: 2,
    // minWidth: 100,
    backgroundColor: Dark.colors.tertiary,
    marginVertical: 4,
  },

  warningContainer: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Dark.colors.warning,
    padding: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    gap: 8,
  },
  warningText: {
    color: Dark.colors.warning,
  },
  tabContainer: {
    height: '100%',
    width: '100%',
    marginHorizontal: 8,
    marginBottom: 10,
  },
  tabHeaderContainer: {
    flexDirection: 'row',
    gap: 12,
    padding: 8,
    borderRadius: 12,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'left',
    justifyContent: 'left',
    paddingVertical: 14,
  paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: Dark.colors.tertiary,

  },
  activeTabButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.2)', 
    
    shadowRadius: 12,

    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  tabButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  activeTabText: {
    color: 'white',
  },
  tabContent: {
    width:'100%',
  },
  tabContentContainer:{
    flexGrow: 1,
    width:'100%',
    height:'100%',
  }

})

export default styles
