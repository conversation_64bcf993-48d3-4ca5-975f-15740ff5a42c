import React, { useCallback, useMemo, useState } from 'react'
import styles from './TopPlayersLeaderboard.style'
import _toUpper from 'lodash/toUpper'
import _split from 'lodash/split'
import _map from 'lodash/map'
import useMediaQuery from 'core/hooks/useMediaQuery'
import Header from 'shared/Header'
import { useRouter } from 'expo-router'
import _isEmpty from 'lodash/isEmpty'
import TopPlayersCategoryLeaderboard from '../../components/TopPlayersCategoryLeaderboard'
import TabButtonWithGradient from 'shared/TabButtonWithGradient'
import { View } from 'react-native'
import {
  TAB_INDICES,
  LEADERBOARD_TYPES,
  TABS,
} from '../../constants/leaderboardConstants'

const LeaderboardPageContainer = ({}) => {
  const { isMobile } = useMediaQuery()
  const [activeTab, setActiveTab] = useState(TAB_INDICES.FRIENDS)
  const router = useRouter()

  const redirectToUserProfile = useCallback(
    ({ username }) => {
      router.push(`profile/${username}`)
    },
    [router]
  )
  return (
    <View style={[styles.mainContainer, !isMobile && styles.expandedContainer]}>
      <Header title="Leaderboard" />
      <View style={styles.container}>
        <View style={styles.tabContainer}>
          <View style={styles.tabHeaderContainer}>
            <TabButtonWithGradient
              isActive={activeTab === TAB_INDICES.FRIENDS}
              onPress={() => setActiveTab(TAB_INDICES.FRIENDS)}
              iconName={TABS[0].iconName}
              label={TABS[0].label}
            />
            <TabButtonWithGradient
              isActive={activeTab === TAB_INDICES.GLOBAL}
              onPress={() => setActiveTab(TAB_INDICES.GLOBAL)}
              iconName={TABS[1].iconName}
              label={TABS[1].label}
            />
          </View>
          <TopPlayersCategoryLeaderboard
            type={
              activeTab === TAB_INDICES.FRIENDS
                ? LEADERBOARD_TYPES.FRIENDS
                : LEADERBOARD_TYPES.GLOBAL
            }
          />
        </View>
      </View>
    </View>
  )
}

export default React.memo(LeaderboardPageContainer)
