import React, {useCallback, useEffect} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {Text} from '@rneui/themed';
import {useRouter} from 'expo-router';
import _isEmpty from 'lodash/isEmpty';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import Header from 'shared/Header';
import PlaceholderRow from '@/src/components/shared/PlaceholderRow';
import UserImage from '@/src/components/atoms/UserImage';
import userReader from '@/src/core/readers/userReader';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from '@/src/core/constants/themes/dark';
import _get from 'lodash/get';
import ErrorView from '@/src/components/atoms/ErrorView';
import _map from 'lodash/map';

import WebBackButton from 'shared/WebBackButton';
import {useSession} from 'modules/auth/containers/AuthProvider';
import TopPlayersPodium from '../../components/TopPlayersPodium';
import {getRatingForTitle} from '../../constants/leaderboardConstants';
import styles from './CategoryLeaderboard.style';
import PaginatedList from '../../../../components/shared/PaginatedList';
import LeaderboardHeader from '../../components/LeaderboardHeader';
import useLeaderboardController from '../../hooks/useLeaderboardController';

const PAGE_SIZE = 50;

const CategoryLeaderboard = ({type, title}) => {
  const {
    error,
    topThreePlayers,
    podiumLoading,
    paginatedData,
    fetchPodiumPlayers,
    fetchPaginatedData,
  } = useLeaderboardController();
  const {isMobile} = useMediaQuery();
  const router = useRouter();
  const headerItems = ['#', 'MATHELETE', 'RATING'];

  const {user} = useSession();

  useEffect(() => {
    fetchPodiumPlayers({type, title});
  }, [fetchPodiumPlayers, type, title]);

  const handleFetchPaginatedData = useCallback(
    async ({pageNumber}) => fetchPaginatedData({type, title, pageNumber}),
    [fetchPaginatedData, type, title],
  );

  const redirectToUserProfile = useCallback(
    ({username}) => {
      router.push(`profile/${username}`);
    },
    [router],
  );

  const redirectToFriendsPage = useCallback(() => {
    router.push(`profile/${user?.username}/friends`);
  }, [router, user]);

  const renderRow = useCallback(
    ({item: itemArg, labelStyle}) => {
      if (_isEmpty(itemArg)) return null;
      const {node: item} = itemArg;
      const categoryRating = getRatingForTitle(title, item);
      return (
        <TouchableOpacity
          style={styles.rowContainer}
          key={item?._id}
          onPress={() => redirectToUserProfile({username: item?.username})}
        >
          <View style={styles.rankColumn}>
            <Text style={[styles.rowLabel, labelStyle]} numberOfLines={1}>
              {item.globalRank}
            </Text>
          </View>
          <View style={styles.profileInfoColumn}>
            <UserImage
              user={item}
              size={30}
              rounded={false}
              style={{
                height: 32,
                width: 32,
                borderRadius: 4,
                overflow: 'hidden',
              }}
            />
            <Text
              style={[styles.rowLabel, labelStyle]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {userReader.username(item)}
            </Text>
          </View>
          <View style={styles.ratingColumn}>
            <Text style={[styles.rowLabel, labelStyle]} numberOfLines={1}>
              {categoryRating}
            </Text>
          </View>
          <View style={styles.rowSeparator}/>
        </TouchableOpacity>
      );
    },
    [redirectToUserProfile, title],
  );

  const renderEmptyContainer = useCallback(
    () => (
      <View style={styles.noDataContainer}>
        <Text style={styles.addMoreFriendsText}>Add More Friends</Text>
        <TouchableOpacity
          style={styles.addFriendsButton}
          onPress={redirectToFriendsPage}
        >
          <MaterialIcons name="add" size={16} color={dark.colors.secondary}/>
          <Text style={styles.addFriendsButtonText}>Add friends</Text>
        </TouchableOpacity>
      </View>
    ),
    [redirectToFriendsPage],
  );

  const renderPlaceholderRow = useCallback(() => <PlaceholderRow/>, []);

  if (error) return <ErrorView error={error}/>;

  const topThree = _map(topThreePlayers, (edge) => _get(edge, 'node'));
  const first = _get(topThree, '[0]');
  const second = _get(topThree, '[1]');
  const third = _get(topThree, '[2]');

  return (
    <View style={[styles.mainContainer, !isMobile && styles.expandedContainer]}>
      <Header title={title}/>
      <WebBackButton containerStyle={{paddingVertical: 0}}/>
      <View style={styles.container}>
        <TopPlayersPodium
          loading={podiumLoading}
          first={first}
          second={second}
          third={third}
          title={title}
        />
        <LeaderboardHeader
          headerItems={headerItems}
          headerStyle={styles.headerContainer}
        />
        <PaginatedList
          placeholderComponent={renderPlaceholderRow}
          fetchData={handleFetchPaginatedData}
          renderItem={renderRow}
          pageSize={PAGE_SIZE}
          data={paginatedData}
          keyExtractor={(item) => item?.node?._id}
          contentContainerStyle={styles.userListStyle}
          listFooterComponent={<View style={{height: 80}}/>}
          emptyListComponent={renderEmptyContainer}
        />
      </View>
    </View>
  );
};

export default React.memo(CategoryLeaderboard);
