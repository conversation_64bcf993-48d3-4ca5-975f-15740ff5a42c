import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import APP_LAYOUT_CONSTANTS from '../../../../core/constants/appLayout';

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    width: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    justifyContent: 'flex-start',
  },
  userListStyle: {
    paddingHorizontal: 24,
  },
  heading: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    marginBottom: 16,
  },
  item: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  loadingIndicator: {
    marginTop: 50,
  },
  textContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    color: 'white',
  },

  // header
  headerContainer: {
    marginHorizontal: 12,
    backgroundColor: dark.colors.primary,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  headerLabelStyle: {
    color: dark.colors.textDark,
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    lineHeight: 20,
  },
  // columns
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 6,
  },
  rankColumn: {
    width: 36,
  },
  rowLabel: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  profileInfoColumn: {
    flex: 1,
    flexBasis: 1,
    flexDirection: 'row',
    gap: 6,
    alignItems: 'center',
  },
  ratingColumn: {
    width: 56,
    alignItems: 'flex-end',
  },

  separator: {
    width: '100%',
    height: 1,
    minHeight: 2,
    minWidth: 100,
    backgroundColor: dark.colors.tertiary,
    marginVertical: 4,
  },

  warningContainer: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: dark.colors.warning,
    padding: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    gap: 8,
  },
  warningText: {
    color: dark.colors.warning,
  },
  contentContainer: {
    width: '92%',
    backgroundColor: dark.colors.gradientBackground,
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 12,
  },
  expandedContainer: {
    paddingHorizontal: 36,
    paddingTop: 24,
    backgroundColor: dark.colors.background,
  },
  category: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  categoryText: {
    fontFamily: 'Montserrat-800',
    fontSize: 11,
    color: 'white',
    letterSpacing: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 8,
  },
  icon: {
    width: 24,
    height: 24,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: dark.colors.text,
  },

  rowSeparator: {
    position: 'absolute',
    left: 0,
    right: 16,
    bottom: 0,
    height: 0.8,
    width: '100%',
    backgroundColor: dark.colors.tertiary,
  },
  noDataContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  addMoreFriendsText: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    color: 'white',
  },
  addFriendsButton: {
    marginTop: 10,
    width: 140,
    height: 32,
    borderWidth: 1,
    borderColor: dark.colors.primary,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  addFriendsButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
  },
});

export default styles;
