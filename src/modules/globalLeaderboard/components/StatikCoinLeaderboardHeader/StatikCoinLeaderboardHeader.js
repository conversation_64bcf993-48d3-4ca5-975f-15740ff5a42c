import React from "react"
import { View, Text } from "react-native"
import styles from "./StatikCoinLeaderboardHeader.style"
import useMediaQuery from "core/hooks/useMediaQuery"

const StatikCoinLeaderboardHeader = () => {
    const { isMobile: isCompactMode } = useMediaQuery()
    return (
        <View style={{ flexDirection: "row", marginTop: 15 }}>
            <Text style={[styles.rowHeadingText, styles.rank, !isCompactMode && { flex: 0.2 }]}>
                #
            </Text>
            <Text style={[styles.rowHeadingText, styles.userInfo]}>
                Mathlete
            </Text>
            <Text style={[styles.rowHeadingText, { textAlign: "right" }, styles.statikCoinsDetail]}>
                Statiks
            </Text>
        </View>
    )
}

export default React.memo(StatikCoinLeaderboardHeader)