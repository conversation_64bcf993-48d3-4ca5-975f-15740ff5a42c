import { Dimensions, StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import APP_LAYOUT_CONSTANTS from '../../../../core/constants/appLayout';

const styles = StyleSheet.create({
  expandedContainer: {
    paddingHorizontal: 36,
    paddingTop: 24,
    backgroundColor: dark.colors.background,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  category: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  categoryText: {
    fontFamily: 'Montserrat-800',
    fontSize: 11,
    color: 'white',
    letterSpacing: 2,
  },
  scrollView: {
    width: '100%',
  },
  container: {
    flex: 1,
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    width: '100%',
    alignSelf: 'center',
    paddingHorizontal: 10,
  },
  scrollContent: {
    flexGrow: 1,
    width: '100%',
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 10,
  },
  cardsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  emptyStateContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    width: '100%',
    height: '100%',
    minHeight: Dimensions.get('window').height * 0.6,
  },
  emptyStateText: {
    fontSize: 11,
    fontFamily: 'Montserrat-700',
    letterSpacing: 0.5,
    color: 'white',
    marginBottom: 8,
  },
  emptyStateSubText: {
    fontSize: 12,
    color: dark.colors.textDark,
    textAlign: 'center',
    fontFamily: 'Montserrat-500',
  },
  pookieImage: {
    width: 96,
    height: 78,
    marginBottom: 8,
  },
  addFriendsButton: {
    marginTop: 20,
    width: 140,
    height: 32,
    borderWidth: 1,
    borderColor: dark.colors.primary,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  addFriendsButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
  },
});

export default styles;
