import React, {useCallback, useEffect, useState} from 'react';
import {Image, ScrollView, Text, TouchableOpacity, View} from 'react-native';
import TopPlayersLeaderboardCard from './components/TopPlayersLeaderboardCard';
import styles from './TopPlayersCategoryLeaderboard.style';
import useGlobalTopPlayers from '../../hooks/useGlobalTopPlayers';
import useFriendsTopPlayers from '../../hooks/useFriendsTopPlayers';
import LEADERBOARD_CATEGORIES, {LEADERBOARD_TYPES} from '../../constants/leaderboardConstants';
import noFriendsPookie from '@/assets/images/pookie/no_friends_pookie.png';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from '@/src/core/constants/themes/dark';
import {useRouter} from 'expo-router';
import {useSession} from '@/src/modules/auth/containers/AuthProvider';

const TopPlayersCategoryUI = ({
                                topPlayers,
                                loading,
                                type,
                                onAddFriends
                              }) => {
  const renderCategoryCard = useCallback((category, index) => {
    const {id, title, icon, ratingKey} = category;
    const players = topPlayers[ratingKey] || EMPTY_ARRAY;
    const isLast = index === LEADERBOARD_CATEGORIES.length - 1;

    return (
      <TopPlayersLeaderboardCard
        key={id}
        title={title}
        type={type}
        icon={icon}
        topPlayers={players}
        loading={loading}
        isLast={isLast}
      />
    );
  }, [topPlayers, loading, type]);

  if (type === LEADERBOARD_TYPES.FRIENDS && !loading && Object.values(topPlayers).every(arr => arr.length === 0)) {
    return (
      <View style={styles.emptyStateContainer}>
        <Image source={noFriendsPookie} style={styles.pookieImage}/>
        <Text style={styles.emptyStateText}>NO FRIENDS TO SHOW</Text>
        <Text style={styles.emptyStateSubText}>
          Add friends to challenge, compete, and see where you rank in your social circle!
        </Text>
        <TouchableOpacity style={styles.addFriendsButton} onPress={onAddFriends}>
          <MaterialIcons name="add" size={16} color={dark.colors.secondary}/>
          <Text style={styles.addFriendsButtonText}>Add friends</Text>
        </TouchableOpacity>
      </View>
    );
  }
  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {LEADERBOARD_CATEGORIES.map((category, index) =>
        renderCategoryCard(category, index)
      )}
    </ScrollView>
  );
};


const TopPlayersCategoryContainer = ({type}) => {
  const [topPlayers, setTopPlayers] = useState({
    globalRating: EMPTY_ARRAY,
    memoryRating: EMPTY_ARRAY,
    abilityRating: EMPTY_ARRAY
  });
  const router = useRouter();
  const {user} = useSession();

  const {data: globalData, loading: globalLoading} = useGlobalTopPlayers();
  const {data: friendsData, loading: friendsLoading} = useFriendsTopPlayers();

  const loading = type === LEADERBOARD_TYPES.GLOBAL ? globalLoading : friendsLoading;
  const data = type === LEADERBOARD_TYPES.GLOBAL ? globalData : friendsData;

  useEffect(() => {
    if (data) {
      const newTopPlayers = {
        globalRating: data.globalRating || EMPTY_ARRAY,
        memoryRating: data.memoryRating || EMPTY_ARRAY,
        abilityRating: data.abilityRating || EMPTY_ARRAY
      };
      setTopPlayers(newTopPlayers);
    }
  }, [data]);

  const handleAddFriends = useCallback(() => {
    router.push(`/profile/${user?.username}/friends`);
  }, [router, user]);

  return (
    <TopPlayersCategoryUI
      topPlayers={topPlayers}
      loading={loading}
      type={type}
      onAddFriends={handleAddFriends}
    />
  );
};

export default React.memo(TopPlayersCategoryContainer);