import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import _map from 'lodash/map';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import Analytics from 'core/analytics';
import { useRouter } from 'expo-router';
import Rive from 'atoms/Rive';
import GoogleLoginButton from 'core/oauth/components/GoogleLoginButton';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAMES } from 'core/constants/pageNames';
import { useSession } from 'modules/auth/containers/AuthProvider';
import dark from 'core/constants/themes/dark';
import Header from './components/Header';
import { splashScreenData } from '../SplashScreen/constants/SplashScreenData';
import DownloadAppQR from './components/DownloadAppQR';
import styles from './LandingPage.style';

const LandingPage = () => {
  const carouselRef = useRef(null);
  const router = useRouter();

  useEffect(() => {
    carouselRef.current?.startAutoplay?.();
  }, []);

  const [activeIndex, setActiveIndex] = useState(0);

  const { createGuestUser } = useSession();

  const loginAsGuest = useCallback(async () => {
    showToast({ type: TOAST_TYPE.LOADING, description: 'Logging in...' });
    Analytics.track(ANALYTICS_EVENTS.ONBOARDING.CLICKED_ON_GUEST_LOGIN, {
      pageName: PAGE_NAMES.ONBOARDING_ANIMATION,
    });
    createGuestUser()
      .then(() => {
        hideToast();
      })
      .catch(() => {});
  }, [createGuestUser]);

  const onProgressChange = useCallback(
    (offsetProgress, absoluteProgress) => {
      const currentIndex = Math.round(absoluteProgress);
      if (currentIndex !== activeIndex) {
        setActiveIndex(currentIndex);
      }
    },
    [activeIndex, setActiveIndex],
  );

  const renderDots = useCallback(
    () => (
      <View style={styles.dotsContainer}>
        {_map(splashScreenData, (_, index) =>
          activeIndex !== index ? (
            <View key={index} style={styles.dot} />
          ) : (
            <View style={styles.activeDot} key={index}>
              <View
                style={[styles.dot, { backgroundColor: dark.colors.textDark }]}
              />
            </View>
          ),
        )}
      </View>
    ),
    [activeIndex],
  );

  const renderGoogleLoginButtonComponent = useCallback(
    () => (
      <View style={styles.googleLoginButtonContainer}>
        <Text style={[styles.newButtonText, { color: dark.colors.primary }]}>
          Login / Sign Up
        </Text>
      </View>
    ),
    [],
  );

  const renderStyledContent = useCallback((content) => {
    if (typeof content === 'string') {
      return <Text style={styles.titleText}>{content}</Text>;
    }

    return (
      <Text style={styles.joinText}>
        {_map(content, (segment, index) => (
          <Text
            key={index}
            style={[
              segment.style || styles.joinText,
              segment.color ? { color: segment.color } : {},
            ]}
          >
            {segment.text}
          </Text>
        ))}
      </Text>
    );
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Header />
      </View>
      <View style={styles.topSection}>
        <View style={styles.rightSection}>
          <Carousel
            ref={carouselRef}
            autoPlayInterval={3000}
            onProgressChange={onProgressChange}
            loop
            width={300}
            height={450}
            autoPlay
            data={splashScreenData}
            scrollAnimationDuration={1000}
            renderItem={({ item, index }) => (
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                key={`${index}`}
              >
                <Rive
                  url={item.animationLink}
                  autoPlay
                  style={{ height: 240, width: 257, resizeMode: 'contain' }}
                />
                <View
                  style={{ justifyContent: 'center', alignItems: 'center' }}
                >
                  <Text style={styles.joinText}>
                    {renderStyledContent(item?.title)}
                  </Text>
                  <Text style={styles.communityText}>{item?.description}</Text>
                </View>
              </View>
            )}
          />
        </View>
        <View style={styles.leftSection}>
          <View
            style={{
              flexDirection: 'row',
              gap: 80,
              alignItems: 'center',
              marginBottom: 20,
            }}
          >
            <Text style={styles.matiksText}>MATIKS</Text>
            <View
              style={{
                height: 3,
                width: 150,
                backgroundColor: dark.colors.secondary,
                alignSelf: 'center',
              }}
            />
          </View>
          <View>
            <Text style={styles.titleText}>
              {`MAKING MATH \nA `}
              <Text
                style={[styles.titleText, { color: dark.colors.secondary }]}
              >
                SPORT
              </Text>
            </Text>
          </View>
          <Text style={styles.descriptionText}>
            Matiks is a community for mathletes, turning math into an
            accessible, exciting sport for everyone.
          </Text>
          <View style={styles.buttonContainer}>
            <GoogleLoginButton
              styles={{ buttonContainerStyle: { maxWidth: 240 } }}
            />
            <TouchableOpacity onPress={loginAsGuest}>
              <Text style={styles.newButtonText}>Play as guest</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      {renderDots()}
      <DownloadAppQR />
    </View>
  );
};

export default LandingPage;
