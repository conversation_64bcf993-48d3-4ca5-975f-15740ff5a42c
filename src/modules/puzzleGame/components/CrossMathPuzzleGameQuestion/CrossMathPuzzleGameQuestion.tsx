import React, { useCallback } from 'react';
import { View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import usePuzzleGameQuestionsState from 'modules/puzzleGame/hooks/usePuzzleGameQuestionState';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import useMediaQuery from 'core/hooks/useMediaQuery';
import CrossMathPuzzleQuestion from 'shared/CrossMathPuzzleQuestion/CrossMathPuzzleQuestion';

const CrossMathPuzzleGameQuestion = () => {
  const { currentQuestion: puzzle, submitAnswer } =
    usePuzzleGameQuestionsState();

  const { isMobile: isCompactMode } = useMediaQuery();

  const onSubmitPuzzle = useCallback(() => {
    submitAnswer?.({ questionId: puzzleReader.id(puzzle) });
  }, [submitAnswer, puzzle]);

  return (
    <View style={{ flex: 1 }}>
      <CrossMathPuzzleQuestion puzzle={puzzle} onSubmitPuzzle={onSubmitPuzzle}>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <View
            style={{
              flex: 1,
              padding: 8,
              gap: isCompactMode ? 32 : 64,
              justifyContent: isCompactMode ? 'space-around' : 'center',
              alignItems: 'center',
            }}
          >
            <CrossMathPuzzleQuestion.Grid />
            <CrossMathPuzzleQuestion.Actions />
            <CrossMathPuzzleQuestion.Options />
          </View>
        </GestureHandlerRootView>
      </CrossMathPuzzleQuestion>
    </View>
  );
};

export default React.memo(CrossMathPuzzleGameQuestion);
