import React from 'react';
import _toNumber from 'lodash/toNumber';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import _isEmpty from 'lodash/isEmpty';
import GameErrorPage from '@/src/modules/game/pages/GameErrorPage';
import Loading from '../../../../components/atoms/Loading';
import GameResultContainer from './CrossMathPuzzleGameResult';
import usePuzzleGameContext, {
  WithPuzzleGameContext,
} from '../../hooks/usePuzzleGameContext';
import { PUZZLE_GAME_STATUS } from '../../constants/puzzleGame';

const CrossMathPuzzleGameResultPage = () => {
  const gameContextValue = usePuzzleGameContext();
  const { game, gameMeta, players } = gameContextValue ?? EMPTY_OBJECT;
  const { loading, error } = gameMeta ?? EMPTY_OBJECT;
  const {
    _id: gameId,
    startTime,
    config,
    gameStatus,
    gameType,
  } = game ?? EMPTY_OBJECT;

  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTimeWithOffset();

  const { timeLimit } = config ?? EMPTY_OBJECT;
  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;

  const hasGameTimeLimitOver = endTime <= currentTime;

  const isEndingGame =
    hasGameTimeLimitOver && gameStatus === PUZZLE_GAME_STATUS.STARTED;

  if (gameStatus === PUZZLE_GAME_STATUS.ENDED) {
    return <GameResultContainer />;
  }

  if (isEndingGame || loading || _isEmpty(game)) {
    return <Loading label="Calculating result..." />;
  }

  return <GameErrorPage />;
};

export default React.memo(WithPuzzleGameContext(CrossMathPuzzleGameResultPage));
