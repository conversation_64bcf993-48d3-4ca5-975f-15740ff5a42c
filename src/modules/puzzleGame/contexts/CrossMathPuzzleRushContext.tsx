import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { getNewPuzzle } from 'modules/puzzles/utils/puzzleGenerator';
import useSubmitPuzzleGameRush from 'modules/puzzleGame/hooks/mutations/useSubmitPuzzleGameRush';
import _isNaN from 'lodash/isNaN';
import _toNumber from 'lodash/toNumber';
import useUserActivityTracker from 'core/hooks/useUserActivityTracker';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

const DIFFICULTY_LEVELS = [
  { gridSize: 5, numOfHints: 2 },
  { gridSize: 5, numOfHints: 2 },
  { gridSize: 5, numOfHints: 1 },
  { gridSize: 5, numOfHints: 1 },
  { gridSize: 5, numOfHints: 0 },
  { gridSize: 5, numOfHints: 0 },
  { gridSize: 7, numOfHints: 6 },
  { gridSize: 7, numOfHints: 6 },
  { gridSize: 7, numOfHints: 5 },
  { gridSize: 7, numOfHints: 5 },
  { gridSize: 7, numOfHints: 4 },
  { gridSize: 7, numOfHints: 4 },
  { gridSize: 7, numOfHints: 3 },
  { gridSize: 7, numOfHints: 3 },
  { gridSize: 7, numOfHints: 2 },
  { gridSize: 7, numOfHints: 2 },
  { gridSize: 7, numOfHints: 1 },
  { gridSize: 7, numOfHints: 1 },
  { gridSize: 7, numOfHints: 0 },
];
const PUZZLE_RUSH = 'PUZZLE_RUSH';

const CrossMathPuzzleRushContext = createContext(null);

export const CrossMathPuzzleRushContextProvider = ({ children }) => {
  const [score, setScore] = useState(0);
  const [difficultyLevel, setDifficultyLevel] = useState(0);
  const [puzzle, setPuzzle] = useState(getNewPuzzle(DIFFICULTY_LEVELS[0]));
  const [timer, setTimer] = useState(0);
  const [isGameActive, setIsGameActive] = useState(true);
  const [gameResults, setGameResults] = useState<any>(null);

  const { submitPuzzleGameRush, loading: submittingPuzzleGameRush } =
    useSubmitPuzzleGameRush();

  const { updateActivity } = useUserActivityTracker();

  useEffect(() => {
    let interval: any;
    if (isGameActive) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isGameActive]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  const onSubmitPuzzle = useCallback(() => {
    setScore((prevScore) => prevScore + 1);

    let newDifficultyLevel = difficultyLevel;
    if (difficultyLevel < DIFFICULTY_LEVELS.length - 1) {
      newDifficultyLevel += 1;
    }
    setDifficultyLevel(newDifficultyLevel);
    setPuzzle(getNewPuzzle(DIFFICULTY_LEVELS[newDifficultyLevel]));
  }, [difficultyLevel]);

  const endGame = useCallback(() => {
    if (!_isNaN(score) && score > 0 && !_isNaN(timer)) {
      Analytics.track(ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.ENDED_PUZZLE_RUSH, {
        score,
        timeSpent: timer,
      });
      submitPuzzleGameRush({
        score,
        timeSpent: timer * 1000, // Converting seconds to milliseconds
      }).then((res) => {
        const { data } = res ?? EMPTY_OBJECT;
        const { submitPuzzleGameRush: submitPuzzleGameRushData } =
          data ?? EMPTY_OBJECT;
        const { bestAllTime, isNewBestScore } =
          submitPuzzleGameRushData ?? EMPTY_OBJECT;
        updateActivity({
          activityType: PUZZLE_RUSH,
          duration: _toNumber(timer) * 1000,
        });
        setIsGameActive(false);
        setGameResults({
          score,
          bestScore: bestAllTime,
          isNewBestScore,
          time: timer,
          coinsGained: score * 2,
          formattedTime: formatTime(timer),
          averageTimePerPuzzle: score > 0 ? Math.round(timer / score) : 0,
          finalDifficultyLevel: difficultyLevel,
        });
      });
    }
  }, [updateActivity, score, timer, difficultyLevel, submitPuzzleGameRush]);

  const resetGame = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.RESTARTED_PUZZLE_RUSH);
    setScore(0);
    setDifficultyLevel(0);
    setTimer(0);
    setIsGameActive(true);
    setGameResults(null);
    setPuzzle(getNewPuzzle(DIFFICULTY_LEVELS[0]));
  }, []);

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.STARTED_PUZZLE_RUSH);
  }, []);

  const value = {
    score,
    timer,
    formatTime,
    puzzle,
    isGameActive,
    gameResults,
    onSubmitPuzzle,
    isSubmittingPuzzleGameRush: submittingPuzzleGameRush,
    endGame,
    resetGame,
    currentDifficulty: DIFFICULTY_LEVELS[difficultyLevel],
  };

  return (
    <CrossMathPuzzleRushContext.Provider value={value}>
      {children}
    </CrossMathPuzzleRushContext.Provider>
  );
};

export const useCrossMathPuzzleRushState = () => {
  const context = useContext(CrossMathPuzzleRushContext);
  if (!context) {
    throw new Error('useGameState must be used within a GameProvider');
  }
  return context;
};
