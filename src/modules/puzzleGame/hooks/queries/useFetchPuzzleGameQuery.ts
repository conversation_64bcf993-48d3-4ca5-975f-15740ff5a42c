import { gql, useQuery } from '@apollo/client';
import { PUZZLE_GAME_FRAGMENT } from 'core/graphql/fragments/puzzleGame';
import _map from 'lodash/map';
import _isNil from 'lodash/isNil';
import { getPuzzleFromMinifiedString } from '../../utils/getPuzzleFromMinifiedString';

const FETCH_PUZZLE_GAME_QUERY = gql`
  ${PUZZLE_GAME_FRAGMENT}
  query GetPuzzleGameById($gameId: ID!) {
    game: getPuzzleGameById(gameId: $gameId) {
      ...CorePuzzleGameFields
    }
  }
`;

const useFetchPuzzleGameQuery = ({ gameId }: { gameId: string }) => {
  const { data, loading, error, refetch } = useQuery(FETCH_PUZZLE_GAME_QUERY, {
    variables: {
      gameId,
    },
    fetchPolicy: 'cache-and-network',
  });

  const gameData = data?.game;
  const { questions: stringifiedPuzzles, ...coreGameDataFields } =
    gameData ?? EMPTY_OBJECT;
  const questions = _map(stringifiedPuzzles, (puzzle) =>
    getPuzzleFromMinifiedString(puzzle),
  );

  return {
    game: _isNil(gameData) ? gameData : { ...coreGameDataFields, questions },
    loading,
    error,
    reFetchGame: refetch,
  };
};

export default useFetchPuzzleGameQuery;
