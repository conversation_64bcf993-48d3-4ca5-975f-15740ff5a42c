import { useCallback } from 'react';
import { gql, useMutation } from '@apollo/client';
import {
  PUZZLE_GAME_DEFAULT_TIME_CONFIG,
  PUZZLE_GAME_TYPES,
} from 'modules/home/<USER>/puzzleGameTypes';
import { PUZZLE_GAME_FRAGMENT } from '@/src/core/graphql/fragments/puzzleGame';

const CREATE_PUZZLE_GAME_QUERY = gql`
  ${PUZZLE_GAME_FRAGMENT}
  mutation CreatePuzzleGame($gameConfig: PuzzleGameConfigInput) {
    game: createPuzzleGame(gameConfig: $gameConfig) {
      ...CorePuzzleGameFields
    }
  }
`;

const NUM_OF_PLAYERS = 2;

const useCreateNewGameQuery = () => {
  const [createPuzzleGameQuery, { data, loading, error }] = useMutation(
    CREATE_PUZZLE_GAME_QUERY,
  );

  const { game: gameData } = data ?? EMPTY_OBJECT;

  const createPuzzleGame = useCallback(
    ({
      numPlayers = NUM_OF_PLAYERS,
      timeLimit = PUZZLE_GAME_DEFAULT_TIME_CONFIG,
      gameType = PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_WITH_FRIEND,
      ...restArgs
    } = EMPTY_OBJECT) => {
      const variables = {
        gameConfig: {
          numPlayers,
          timeLimit,
          gameType,
          ...restArgs,
        },
      };
      return createPuzzleGameQuery({ variables });
    },
    [createPuzzleGameQuery],
  );

  return {
    game: gameData,
    createPuzzleGame,
    creatingPuzzleGame: loading,
    error,
  };
};

export default useCreateNewGameQuery;
