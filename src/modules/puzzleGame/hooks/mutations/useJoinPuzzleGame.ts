import { gql, useMutation } from '@apollo/client';

import { useCallback } from 'react';
import { PUZZLE_GAME_FRAGMENT } from 'core/graphql/fragments/puzzleGame';

const JOIN_PUZZLE_GAME_QUERY = gql`
  ${PUZZLE_GAME_FRAGMENT}
  mutation JoinPuzzleGame($gameId: ID!) {
    joinPuzzleGame(gameId: $gameId) {
      ...CorePuzzleGameFields
    }
  }
`;

const useJoinGameQuery = () => {
  const [joinPuzzleGameQuery, { loading }] = useMutation(
    JOIN_PUZZLE_GAME_QUERY,
  );

  const joinPuzzleGame = useCallback(
    ({ gameId }: { gameId: string }) =>
      joinPuzzleGameQuery({ variables: { gameId } }),
    [joinPuzzleGameQuery],
  );

  return {
    joinPuzzleGame,
    isJoiningPuzzleGame: loading,
  };
};

export default useJoinGameQuery;
