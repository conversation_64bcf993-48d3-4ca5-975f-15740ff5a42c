import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _map from 'lodash/map';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import { PUZZLE_GAME_FRAGMENT } from 'core/graphql/fragments/puzzleGame';

const START_PUZZLE_GAME_MUTATION = gql`
  ${PUZZLE_GAME_FRAGMENT}
  mutation StartGame($gameId: ID!) {
    startPuzzleGame(gameId: $gameId) {
      ...CorePuzzleGameFields
    }
  }
`;

const useStartPuzzleGame = () => {
  const [startPuzzleGameMutation, { data = {}, loading, error }] = useMutation(
    START_PUZZLE_GAME_MUTATION,
  );

  const { startGameData } = data;
  const { encryptedQuestions } = startGameData ?? EMPTY_OBJECT;
  const questions = _map(encryptedQuestions, decryptJsonData);

  const startPuzzleGame = useCallback(
    async (gameId: string) =>
      startPuzzleGameMutation({
        variables: {
          gameId,
        },
      }),
    [startPuzzleGameMutation],
  );

  return {
    startGameData: { ...startGameData, questions },
    startPuzzleGame,
    startingGame: loading,
    error,
  };
};

export default useStartPuzzleGame;
