import { gql, useMutation } from '@apollo/client';

import { PUZZLE_GAME_FRAGMENT } from 'core/graphql/fragments/puzzleGame';
import { useCallback } from 'react';

const END_GAME_QUERY = gql`
  ${PUZZLE_GAME_FRAGMENT}
  mutation EndPuzzleGame($gameId: ID!) {
    endPuzzleGame(gameId: $gameId) {
      ...CorePuzzleGameFields
    }
  }
`;

const useEndPuzzleGameQuery = () => {
  const [endPuzzleGameQuery, { loading }] = useMutation(END_GAME_QUERY);

  const endPuzzleGame = useCallback(
    ({ gameId }: { gameId: string }) =>
      endPuzzleGameQuery({ variables: { gameId } }),
    [endPuzzleGameQuery],
  );

  return {
    endPuzzleGame,
    endingGame: loading,
  };
};

export default useEndPuzzleGameQuery;
