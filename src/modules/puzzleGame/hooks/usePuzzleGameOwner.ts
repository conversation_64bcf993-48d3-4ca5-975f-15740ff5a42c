import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useCallback } from 'react';
import userReader from 'core/readers/userReader';
import puzzleGameReader from 'core/readers/puzzleGameReader';
import { PuzzleGame } from 'modules/puzzleGame/types/puzzleGame';

const usePuzzleGameOwner = () => {
  const { user = EMPTY_OBJECT } = useSession();

  const checkIsGameOwner = useCallback(
    ({ game }: { game: PuzzleGame }) =>
      userReader.id(user) === puzzleGameReader.createdBy(game),
    [user],
  );

  return {
    checkIsGameOwner,
  };
};

export default usePuzzleGameOwner;
