import React from 'react'
import { Animated, Image, Text, TouchableOpacity, View } from 'react-native'
import dark from 'core/constants/themes/dark'
import userReader from 'core/readers/userReader'
import _map from 'lodash/map'
import UserImage from 'atoms/UserImage'
import StatikCoinsIcon from '@/assets/images/icons/statikCoins.png'
import { GAME_TYPES } from '@/src/modules/home/<USER>/gameTypes'
import styles from '../LeagueLeaderboard/LeagueLeaderboardPage.style'
import _isNil from 'lodash/isNil'

const getActivityIcon = (activityType) => {
  switch (activityType) {
    case GAME_TYPES.FASTEST_FINGER:
      return (
        <Image
          source={require('@/assets/images/fastestFinger.png')}
          style={styles.activityIcon}
        />
      )
    case GAME_TYPES.FLASH_ANZAN:
      return (
        <Image
          source={require('@/assets/images/flashAnzan.png')}
          style={styles.activityIcon}
        />
      )
    case GAME_TYPES.ONLINE_CHALLENGE:
      return (
        <Image
          source={require('@/assets/images/sprint.png')}
          style={styles.activityIcon}
        />
      )
    case GAME_TYPES.GROUP_PLAY:
      return (
        <Image
          source={require('@/assets/images/Friend.png')}
          style={styles.activityIcon}
        />
      )
    case GAME_TYPES.PLAY_ONLINE:
      return (
        <Image
          source={require('@/assets/images/duel.png')}
          style={styles.activityIcon}
        />
      )
    case GAME_TYPES.PRACTICE:
      return (
        <Image
          source={require('@/assets/images/time.png')}
          style={styles.activityIcon}
        />
      )
    case GAME_TYPES.SUMDAY_SHOWDOWN:
      return (
        <Image
          source={require('@/assets/images/icons/sumday_showdown.png')}
          style={styles.activityIcon}
        />
      )
    case 'CONTEST':
      return (
        <Image
          source={require('assets/images/icons/80in8.png')}
          style={styles.activityIcon}
        />
      )
    default:
      return null
  }
}

const LeaderboardParticipantRow = ({
  item,
  isExpanded,
  onToggle,
  containerStyle,
}) => {
  const animatedValue = React.useRef(
    new Animated.Value(isExpanded ? 1 : 0)
  ).current

  const processedActivities = React.useMemo(() => {
    let nullIconCoins = 0
    const activities = []

    _map(item?.activitySummary, (activity) => {
      const icon = getActivityIcon(activity.activity)
      if (icon) {
        activities.push({
          ...activity,
          icon,
        })
      } else {
        nullIconCoins += activity.coins
      }
    })

    if (nullIconCoins > 0) {
      activities.push({
        activity: 'OTHER',
        coins: nullIconCoins,
        icon: null,
        isOther: true,
      })
    }

    return activities
  }, [item?.activitySummary])

  React.useEffect(() => {
    if (!_isNil(item?.activitySummary)) {
      Animated.spring(animatedValue, {
        toValue: isExpanded ? 1 : 0,
        useNativeDriver: true,
        tension: 20,
        friction: 7,
      }).start()
    }
  }, [isExpanded, animatedValue, item?.activitySummary])

  const containerTranslateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -40],
  })

  const nameOpacity = animatedValue.interpolate({
    inputRange: [0, 0.5],
    outputRange: [1, 0],
  })

  const summaryTranslateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [100, 0],
  })

  const summaryTranslateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [10, 0],
  })

  const summaryOpacity = animatedValue.interpolate({
    inputRange: [0.5, 1],
    outputRange: [0, 1],
  })

  const statikCoinsOpacity = animatedValue.interpolate({
    inputRange: [0, 0.3],
    outputRange: [1, 0],
  })

  if (_isNil(item)) {
    return null
  }

  return (
    <TouchableOpacity onPress={onToggle} activeOpacity={0.7}>
      <View style={[styles.row, containerStyle]}>
        <Text style={styles.rank}>{item?.rank}</Text>

        <Animated.View
          style={[
            { flexDirection: 'row', gap: 5, flex: 3, alignItems: 'center' },
            { transform: [{ translateX: containerTranslateX }] },
          ]}
        >
          <UserImage
            user={item?.user}
            style={{
              height: 32,
              width: 32,
              borderRadius: 4,
              overflow: 'hidden',
              borderWidth: 1,
              borderColor: dark.colors.tertiary,
            }}
            rounded={false}
          />

          <Animated.View
            style={{
              opacity: nameOpacity,
              position: 'absolute',
              left: 40,
            }}
          >
            <Text style={styles.name} numberOfLines={1}>
              {userReader.username(item?.user)}
            </Text>
          </Animated.View>

          <Animated.View
            style={{
              position: 'absolute',
              left: 40,
              opacity: summaryOpacity,
              transform: [
                { translateX: summaryTranslateX },
                { translateY: summaryTranslateY },
              ],
              flexDirection: 'row',
              alignItems: 'center',
              flexWrap: 'nowrap',
              // justifyContent: 'flex-end',
            }}
          >
            {processedActivities.map((activity, index) => (
              <View
                key={index}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginRight: 12,
                }}
              >
                {activity.isOther ? (
                  <Image
                    source={StatikCoinsIcon}
                    style={{ height: 15, width: 15 }}
                  />
                ) : (
                  activity.icon
                )}
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginLeft: 4,
                  }}
                >
                  <Text style={[styles.score, { fontSize: 12 }]}>
                    {activity.coins}
                  </Text>
                </View>
              </View>
            ))}
          </Animated.View>
        </Animated.View>

        <Animated.View
          style={{
            flexDirection: 'row',
            gap: 3,
            flex: 3,
            alignItems: 'center',
            justifyContent: 'flex-end',
            opacity: statikCoinsOpacity,
          }}
        >
          <Image source={StatikCoinsIcon} style={{ height: 15, width: 15 }} />
          <Text style={styles.score} numberOfLines={1}>
            {item?.statikCoins}
          </Text>
        </Animated.View>
      </View>
    </TouchableOpacity>
  )
}

export default React.memo(LeaderboardParticipantRow)
