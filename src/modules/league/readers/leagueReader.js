import _property from 'lodash/property';

const leagueReader = {
  hostedByV2: _property('hostedByV2'),
  id: _property('_id'),
  name: _property('name'),
  registrationStart: _property('registrationStart'),
  registrationEnd: _property('registrationEnd'),
  leagueStart: _property('leagueStart'),
  leagueEnd: _property('leagueEnd'),
  // Nested properties for hostedByV2
  hostedByName: _property(['hostedByV2', 'name']),
  hostedByLogo: _property(['hostedByV2', 'logo']),
};

export default leagueReader;
