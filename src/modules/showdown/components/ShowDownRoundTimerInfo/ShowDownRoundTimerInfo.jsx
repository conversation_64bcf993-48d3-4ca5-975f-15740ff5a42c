import React, { useMemo } from 'react';
import { Image, Text, View } from 'react-native';
import styles from './ShowDownRoundTimerInfo.style';
import timerIcon from '../../../../../assets/images/timer.png';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import showdownReader from '../../readers/showdownReader';
import useMediaQuery from '../../../../core/hooks/useMediaQuery';

const ShowDownRoundTimerInfo = React.memo(({ state }) => {
  const {
    roundEndsAt,
    breakEndsAt,
    currentRoundInfo,
    isRoundActive,
    currentRound,
    showdown,
    hasUserGotBye,
  } = state;
  const totalRoundCounts = showdownReader.roundsCount(showdown);
  const { isMobile } = useMediaQuery();
  const roundEndTimer = useCountDownTimer({
    targetTimeStamp: roundEndsAt,
  });
  const breakEndTimer = useCountDownTimer({
    targetTimeStamp: breakEndsAt,
  });

  const currentTime = getCurrentTime();

  const isBreakTime = currentTime > roundEndsAt && currentTime < breakEndsAt;

  const { label, time } = useMemo(() => {
    if (isBreakTime) {
      if (currentRound === totalRoundCounts) {
        return { label: `Showdown Ends in`, time: breakEndTimer };
      }
      return {
        label: isMobile
          ? `Break Time `
          : `Break time before Round ${currentRound + 1}`,
        time: breakEndTimer,
      };
    }

    if (isRoundActive) {
      return { label: `Round ${currentRound} Ends In `, time: roundEndTimer };
    }
    return EMPTY_OBJECT;
  }, [
    isBreakTime,
    totalRoundCounts,
    isRoundActive,
    currentRoundInfo,
    breakEndTimer,
    currentRound,
    roundEndTimer,
    showdown,
    hasUserGotBye,
    isMobile,
  ]);

  if (_isEmpty(label)) {
    return null;
  }

  return (
    <View style={styles.infoItem}>
      <View style={styles.iconContainer}>
        <Image source={timerIcon} style={styles.iconStyle} />
      </View>
      <View style={styles.info}>
        <Text style={[styles.infoText, isMobile && styles.infoTextMobile]}>
          {label}
        </Text>
        <Text style={[styles.infoNumber, isMobile && styles.infoNumberMobile]}>
          {time}
        </Text>
      </View>
    </View>
  );
});

ShowDownRoundTimerInfo.propTypes = {
  state: PropTypes.object.isRequired,
};

const ShowDownRoundTimerInfoContainer = (props) => {
  const { state } = props;
  const { hasUserRegistered } = state;

  // should be only shown when user is registered and contest is Live
  if (!hasUserRegistered) {
    return null;
  }

  return <ShowDownRoundTimerInfo {...props} />;
};

export default React.memo(ShowDownRoundTimerInfoContainer);
