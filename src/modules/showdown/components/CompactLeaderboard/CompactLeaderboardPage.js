import React, { useCallback, useMemo } from 'react';
import { View, Text } from 'react-native';
import styles from './CompactLeaderboardPage.style'
import dark from '../../../../core/constants/themes/dark';
import useGoBack from 'navigator/hooks/useGoBack';
import { getTimeSpentByUser } from '../../utils/contest';
import { getFormattedTimeWithMS } from '../../../../core/utils/general';
import useGetContestLeaderboard from '../../hooks/useGetContestLeaderboard'
import PaginatedList from '../../../../components/shared/PaginatedList';
import { CONTEST_STATUS } from '../../constants/index';
import Header from 'shared/Header';
import PlaceholderRow from '../../../../components/shared/PlaceholderRow';
import userReader from "../../../../core/readers/userReader";

const PAGE_SIZE = 50;

const CompactLeaderboardPage = ({ contest }) => {
    const isContestLive = contest.status === CONTEST_STATUS.ONGOING;
    const { fetchLeaderboard } = useGetContestLeaderboard({ contestId: contest?._id, isLive: isContestLive, pageSize: PAGE_SIZE });
    const { goBack } = useGoBack();

    const fetchData = useCallback(async ({ pageNumber }) => {
        const response = await fetchLeaderboard({ pageNumber });
        const { data } = response;
        const { getContestLeaderboard: participantsObject } = data;
        const { participants, totalParticipants } = participantsObject;
        return { data: participants, totalItems: totalParticipants };
    }, []);

    const renderParticipant = useCallback(({ item }) => {
        const timeSpent = getTimeSpentByUser({ contest, participantSubmission: item });
        const formattedTime = getFormattedTimeWithMS(timeSpent);

        return (
            <View style={styles.row}>
                <Text style={styles.rank}>{item?.rank}</Text>
                <Text style={styles.name} numberOfLines={1}>{userReader.username(item?.user)}</Text>
                <Text style={styles.score} numberOfLines={1}>{item?.score}</Text>
                <Text style={styles.time} numberOfLines={1}>{formattedTime}</Text>
            </View>
        );
    }, [contest]);

    const renderHeader = useCallback(() => (
        <View style={styles.row}>
            <Text style={[styles.rank, { color: dark.colors.textDark }]}>{'#'}</Text>
            <Text style={[styles.name, { color: dark.colors.textDark }]} numberOfLines={1}>{'Name'}</Text>
            <Text style={[styles.score, { color: dark.colors.textDark }]}>{'Score'}</Text>
            <Text style={[styles.time, { color: dark.colors.textDark }]}>{'Time'}</Text>
        </View>
    ), []);

    const rendedPlaceHolder = useCallback(() => (
        <View>
            <PlaceholderRow />
            <PlaceholderRow />
            <PlaceholderRow />
        </View>
    ), [])

    return (
        <View style={{ flex: 1, width: '100%' }}>
            <Header title={contest?.name} />
            <View style={{ paddingHorizontal: 16, flex: 1 }}>
                <PaginatedList
                    placeholderComponent={rendedPlaceHolder}
                    fetchData={fetchData}
                    renderItem={renderParticipant}
                    renderHeader={renderHeader}
                    pageSize={PAGE_SIZE}
                    keyExtractor={(item, index) => `${item.user?._id.toString()} - ${index}`}
                    contentContainerStyle={styles.list}
                    listFooterComponent={<View style={{ height: 80 }}></View>}
                />
            </View>
        </View>
    );
};

export default React.memo(CompactLeaderboardPage);
