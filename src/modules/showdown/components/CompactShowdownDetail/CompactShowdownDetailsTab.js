import React, { useCallback, useState, useMemo, useEffect } from 'react';
import { View, Dimensions, Text } from 'react-native';
import { TabBar, TabView } from 'react-native-tab-view';
import _isEmpty from 'lodash/isEmpty';
import dark from 'core/constants/themes/dark';
import ShowdownDetailRoute from './CompactDetailRoute';
import styles from './CompactShowdownDetail.style';

import Analytics from 'core/analytics';
import { getShowdownPropertiesToTrack } from '../../utils/showdownEvents';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import { TAB_KEY_VS_EVENT, TAB_KEYS } from '../../constants/showdownDetails';
import useShowdownFixtures from '../../hooks/query/useFixturesDetails';
import useShowdownLeaderboard from '../../hooks/query/useLeaderboardDetails';
import ShowdownLeaderboard from '../ShowdownLeaderboard';
import Fixtures from '../Fixtures';
import showdownReader from '../../readers/showdownReader';

const initialLayout = { width: Dimensions.get('window').width };

const CompactShowdownDetailsTabBarView = React.memo(({ state }) => {
  const {
    showdown: showdownDetails,
    isLive,
    hasUserRegistered,
    hasEnded,
  } = state;
  const showdownId = showdownReader.id(showdownDetails);
  const [index, setIndex] = useState(0);

  const { fixtures, loading: fixturesLoading } = useShowdownFixtures({
    showdownId,
  });

  const routes = useMemo(() => {
    const availableRoutes = [];
    if (!hasUserRegistered) {
      availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
      return availableRoutes;
    }
    availableRoutes.push({
      key: TAB_KEYS.FIXTURES,
      title: 'Fixtures',
    });
    if (isLive || hasEnded) {
      availableRoutes.push({
        key: TAB_KEYS.LEADERBOARD,
        title: 'Leaderboard',
      });
    }
    availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
    return availableRoutes;
  }, [isLive, hasUserRegistered, hasEnded, fixtures]);

  const onIndexChange = useCallback(
    (updatedIndex) => {
      setIndex(updatedIndex);
      const route = routes[updatedIndex];
      if (TAB_KEY_VS_EVENT?.[route?.key]) {
        Analytics.track(TAB_KEY_VS_EVENT[route.key], {
          ...getShowdownPropertiesToTrack({
            showdown: showdownDetails,
          }),
          [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
        });
      }
    },
    [routes, showdownDetails, setIndex],
  );

  const renderScene = useCallback(
    ({ route }) => {
      switch (route.key) {
        case TAB_KEYS.FIXTURES:
          return (
            <Fixtures
              fixtures={fixtures}
              state={state}
              loading={fixturesLoading}
            />
          );
        case TAB_KEYS.LEADERBOARD:
          return (
            <ShowdownLeaderboard
              loading={fixturesLoading}
              showdown={showdownDetails}
            />
          );
        case TAB_KEYS.DETAILS:
          return (
            <ShowdownDetailRoute
              state={state}
              isLive={isLive}
              hasEnded={hasEnded}
            />
          );
        default:
          return <ShowdownDetailRoute state={state} isLive={isLive} />;
      }
    },
    [fixtures, state, isLive, hasEnded, fixturesLoading],
  );

  const renderTabBar = useCallback(
    (props) =>
      routes.length > 1 ? (
        <View style={styles.tabBarContainer}>
          <TabBar
            {...props}
            indicatorStyle={styles.indicator}
            style={styles.tabBar}
            tabStyle={styles.tabStyle}
            labelStyle={styles.label}
            activeColor={dark.colors.secondary}
            inactiveColor={dark.colors.textDark}
            // scrollEnabled={false}
            renderLabel={({ route, focused, color }) => (
              <Text style={{ ...styles.label, color }}>{route.title}</Text>
            )}
          />
          <View style={styles.fullWidthLine} />
        </View>
      ) : null,
    [routes],
  );

  useEffect(() => {
    if (index >= routes.length) {
      setIndex(0);
    }
  }, [routes, index]);

  return (
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={onIndexChange}
      swipeEnabled={false}
      initialLayout={initialLayout}
      renderTabBar={renderTabBar}
    />
  );
});

export default React.memo(CompactShowdownDetailsTabBarView);
