import React from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import ExpandedClubDetailCard from 'modules/clubs/components/ClubDetailCard/ExpandedClubDetailCard';
import CompactClubDetailCard from 'modules/clubs/components/ClubDetailCard/CompactClubDetailCard';
import { ClubInfo } from 'modules/clubs/hooks/queries/useGetClubById';

const ClubDetailCard = ({
  clubInfo,
  clubDetailsList,
  joinClub,
  isJoiningClub,
}: {
  clubInfo: ClubInfo;
  joinClub: any;
  isJoiningClub: boolean;
  clubDetailsList: any;
}) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const ClubDetailCardComponent = isCompactMode
    ? CompactClubDetailCard
    : ExpandedClubDetailCard;

  return (
    <ClubDetailCardComponent
      clubInfo={clubInfo}
      clubDetailsList={clubDetailsList}
      joinClub={joinClub}
      isJoiningClub={isJoiningClub}
    />
  );
};

export default React.memo(ClubDetailCard);
