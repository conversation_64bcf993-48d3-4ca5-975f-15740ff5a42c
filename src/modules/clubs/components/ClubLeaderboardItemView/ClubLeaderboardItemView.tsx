import UserImage from '@/src/components/atoms/UserImage';
import userReader from '@/src/core/readers/userReader';
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import styles from './ClubLeaderboardItemView.style';

const ClubLeaderboardItemView = ({
  userInfo,
  rank,
}: {
  userInfo: any;
  rank: number;
}) => {
  const navigateToUserProfile = () => {
    router.push(`/profile/${userReader.username(userInfo)}`);
  };

  return (
    <TouchableOpacity style={styles.container} onPress={navigateToUserProfile}>
      <Text style={styles.rankText}>{rank}</Text>
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
        <UserImage rounded={false} user={userInfo} style={styles.userImage} />
        <View style={{ gap: 2 }}>
          <Text style={styles.usernameText}>
            {userReader.username(userInfo)}
          </Text>
          <Text style={styles.ratingText}>{userReader.rating(userInfo)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default React.memo(ClubLeaderboardItemView);
