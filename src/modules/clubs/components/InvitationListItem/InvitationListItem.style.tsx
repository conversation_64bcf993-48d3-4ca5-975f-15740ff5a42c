import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: dark.colors.tertiary,
  },
  overlayStyle: {
    borderRadius: 8,
    padding: 0,
    position: 'absolute',
    maxWidth: 160,
    maxHeight: 45,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: dark.colors.primary,
  },
  userImage: {
    height: 40,
    width: 40,
    borderRadius: 4,
    overflow: 'hidden',
  },
  usernameText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Montserrat-400',
  },
  ratingText: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-500',
  },
  rejectText: {
    color: 'white',
    fontSize: 10,
    fontFamily: 'Montserrat-500',
  },
  button: {
    flexDirection: 'row',
    gap: 4,
    paddingHorizontal: 5,
    paddingVertical: 4,
    borderColor: dark.colors.defeatColor,
    borderWidth: 1,
    borderRadius: 20,
  },
});

export default styles;
