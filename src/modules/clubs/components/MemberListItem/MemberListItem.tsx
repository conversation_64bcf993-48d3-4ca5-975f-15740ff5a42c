import React, { useCallback, useRef, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import userReader from '@/src/core/readers/userReader';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import UserImage from '@/src/components/atoms/UserImage';
import { Entypo } from '@expo/vector-icons';
import dark from '@/src/core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import { showPopover } from 'molecules/Popover/Popover';
import { router } from 'expo-router';
import MemeberCardOptionsPopover from '../MemeberCardOptionsPopover/MemeberCardOptionsPopover';
import styles from './MemberListItem.style';

interface MemberListItemProps {
  memberInfo: any;
  onRoleChange?: (memberId: string, newRole: string) => void;
  onRemove: Function;
}

const MemberListItem: React.FC<MemberListItemProps> = ({
  memberInfo,
  onRoleChange,
  onRemove,
}) => {
  const cardRef = useRef();

  const { isMobile: isCompactMode } = useMediaQuery();

  const { userId: currUserId } = useSession();

  const memberUserId = _get(memberInfo, 'userId', '');

  const [overlayStyle, setOverlayStyle] = useState(styles.overlayStyle);

  const onCardLayoutChange = useCallback(() => {
    cardRef.current?.measure((x, y, width, height, pageX, pageY) => {
      setOverlayStyle([
        styles.overlayStyle,
        { top: pageY, right: isCompactMode ? pageX : pageX + 180 },
      ]);
    });
  }, [cardRef, setOverlayStyle, isCompactMode]);

  const user = _get(memberInfo, 'memberInfo', EMPTY_OBJECT);

  const onPressOptions = useCallback(() => {
    showPopover({
      content: (
        <MemeberCardOptionsPopover
          onRemoveMemberPressed={onRemove}
          memberInfo={memberInfo}
        />
      ),
      style: overlayStyle,
    });
  }, [overlayStyle, onRemove, memberInfo]);

  const navigateToUserProfile = useCallback(() => {
    router.push(`/profile/${userReader.username(user)}`);
  }, [user]);

  const userNameText = _isEqual(memberUserId, currUserId)
    ? `${userReader.username(user)} (You)`
    : userReader.username(user);

  return (
    <TouchableOpacity
      style={styles.container}
      ref={cardRef}
      onLayout={onCardLayoutChange}
      onPress={navigateToUserProfile}
    >
      <View style={{ flexDirection: 'row', gap: 5 }}>
        <UserImage user={user} style={styles.userImage} rounded={false} />
        <View style={{ gap: 2 }}>
          <Text style={styles.usernameText}>{userNameText}</Text>
          <Text style={styles.ratingText}>{userReader.rating(user)}</Text>
        </View>
      </View>
      {!_isEqual(memberUserId, currUserId) && (
        <Entypo
          name="dots-three-vertical"
          size={13}
          color={dark.colors.textDark}
          onPress={onPressOptions}
        />
      )}
    </TouchableOpacity>
  );
};

export default MemberListItem;
