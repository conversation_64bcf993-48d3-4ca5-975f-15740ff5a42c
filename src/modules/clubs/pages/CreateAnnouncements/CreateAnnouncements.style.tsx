import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
  },
  textFormField: {
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
    outlineStyle: 'none',
    // maxWidth:400,
    padding: 12,
    fontSize: 16,
    color: '#fff',
    backgroundColor: '#333',
  },
  focusedInputField: {
    outlineColor: dark.colors.secondary,
    borderWidth: 1,
    outlineColor: dark.colors.secondary,
    outlineStyle: 'none',
    borderColor: dark.colors.secondary,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: '#fff',
    backgroundColor: 'transparent',
  },
  labelText: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-400',
  },
});

export default styles;
