import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  textFormField: {
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
    outlineStyle: 'none',
    // maxWidth:400,
    padding: 12,
    fontSize: 16,
    color: '#fff',
    backgroundColor: '#333',
  },
  focusedInputField: {
    outlineColor: dark.colors.secondary,
    borderWidth: 1,
    outlineColor: dark.colors.secondary,
    outlineStyle: 'none',
    borderColor: dark.colors.secondary,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    color: '#fff',
    backgroundColor: 'transparent',
  },
  topBarContainer: {
    height: 192,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  backgroundImage: {
    width: '100%',
    backgroundColor: dark.colors.gradientBackground,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    height: 161,
  },
  clubImageContainer: {
    width: '100%',
    position: 'absolute',
    justifyContent: 'center',
    bottom: 0,
    alignItems: 'center',
  },
  clubImageView: {
    height: 80,
    width: 80,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    // borderColor: 'red',
  },
});

export default styles;
