import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';
import _isEmpty from 'lodash/isEmpty';
import useGetClubAnnouncements from 'modules/clubs/hooks/queries/useGetClubAnnouncements';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';

const CREATE_ANNOUNCEMENT_MUTATION = gql`
  mutation CreateAnnouncement($input: CreateClubAnnouncementInput!) {
    createClubAnnouncement(input: $input) {
      id
      clubId
      title
      content
      createdAt
      createdBy
    }
  }
`;

interface AnnouncementInfoInput {
  clubId: string;
  title: string;
  content: string;
}

const useCreateAnnouncement = () => {
  const [createAnnouncementMutationQuery, { loading, error }] = useMutation(
    CREATE_ANNOUNCEMENT_MUTATION,
  );

  const { updateClubAnnouncementsCache } = useGetClubAnnouncements({
    pageSize: 50,
  });

  const { user } = useSession();

  const [isCreatingAnnouncement, setIsCreatingAnnouncement] =
    useState<boolean>(false);

  const createAnnouncement = useCallback(
    async ({
      announcementInfo,
    }: {
      announcementInfo: AnnouncementInfoInput;
    }) => {
      if (isCreatingAnnouncement) {
        return false;
      }
      try {
        setIsCreatingAnnouncement(true);
        const response = await createAnnouncementMutationQuery({
          variables: {
            input: {
              ...announcementInfo,
            },
          },
        });

        const { data } = response;
        const { createClubAnnouncement: announcement } = data ?? EMPTY_OBJECT;

        if (_isEmpty(announcement)) {
          return false;
        }

        updateClubAnnouncementsCache({
          updateCacheClubId: announcementInfo.clubId,
          addedItems: [
            {
              ...announcement,
              creatorInfo: {
                username: userReader.username(user),
                profileImageUrl: userReader.profileImageUrl(user),
              },
            },
          ],
          pageNumber: 1,
        });

        return true;
      } catch (e) {
        setIsCreatingAnnouncement(false);
        return false;
      } finally {
        setIsCreatingAnnouncement(false);
      }
    },
    [
      createAnnouncementMutationQuery,
      isCreatingAnnouncement,
      updateClubAnnouncementsCache,
      user,
    ],
  );

  return {
    createAnnouncement,
    isCreatingAnnouncement,
    error,
  };
};

export default useCreateAnnouncement;
