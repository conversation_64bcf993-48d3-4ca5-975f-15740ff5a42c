import { gql, useMutation } from '@apollo/client';
import { useCallback, useState } from 'react';
import useGetClubEvents from 'modules/clubs/hooks/queries/useGetClubEvents';
import _isEmpty from 'lodash/isEmpty';

const CREATE_EVENT_FOR_CLUB_MUTATION = gql`
  mutation CreateClubEvent($input: CreateClubEventInput!) {
    createClubEvent(input: $input) {
      id
      clubId
      title
      description
      clubEventType
      startTime
      createdBy
      createdAt
    }
  }
`;

export type ClubEventType = 'CONTEST_80_IN_8' | 'SUMDAY_SHOWDOWN';

export type EventVisibility = 'PUBLIC' | 'PRIVATE';

interface PlayerSettingInput {
  minRating: number;
  maxRating: number;
}

interface GameConfigInput {
  timeLimit: number;
  numPlayers?: number;
  gameType: string;
  questionTags?: string[];
  difficultyLevel?: number[];
  maxTimePerQuestion?: number;
}

interface ClubEventInfoInput {
  clubId: string;
  title: string;
  clubEventType: ClubEventType;
  startTime: string;
  description: string;
  gameConfig?: GameConfigInput;
  ratedEvent?: boolean;
  playerSetting?: PlayerSettingInput;
  openToAll?: boolean;
  visibility?: EventVisibility;
}

const useCreateEventForClub = () => {
  const [createEventForClubMutationQuery, { loading, error }] = useMutation(
    CREATE_EVENT_FOR_CLUB_MUTATION,
  );

  const { updateClubEventsCache } = useGetClubEvents({ pageSize: 50 });

  const [isCreatingEventForClub, setIsCreatingEventForClub] =
    useState<boolean>(false);

  const createEventForClub = useCallback(
    async ({ eventInfo }: { eventInfo: ClubEventInfoInput }) => {
      if (isCreatingEventForClub) {
        return false;
      }
      try {
        setIsCreatingEventForClub(true);
        const response = await createEventForClubMutationQuery({
          variables: {
            input: {
              ...eventInfo,
            },
          },
        });

        const { data } = response;
        const { createClubEvent: clubEvent } = data ?? EMPTY_OBJECT;
        if (_isEmpty(clubEvent)) {
          return false;
        }

        updateClubEventsCache({
          updateCacheClubId: clubEvent.clubId,
          addedItems: [
            {
              ...clubEvent,
            },
          ],
          pageNumber: 1,
        });
        return true;
      } catch (e) {
        setIsCreatingEventForClub(false);
        return false;
      } finally {
        setIsCreatingEventForClub(false);
      }
    },
    [
      createEventForClubMutationQuery,
      isCreatingEventForClub,
      updateClubEventsCache,
    ],
  );

  return {
    createEventForClub,
    isCreatingEventForClub,
    error,
  };
};

export default useCreateEventForClub;
