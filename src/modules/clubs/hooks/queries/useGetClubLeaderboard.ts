import { gql, useLazyQuery } from '@apollo/client';
import { useCallback } from 'react';

const GET_CLUB_LEADERBOARD = gql`
  query GetClubLeaderboard($clubId: ID!, $page: Int, $pageSize: Int) {
    getClubLeaderboard(clubId: $clubId, page: $page, pageSize: $pageSize) {
      results {
        user {
          _id
          name
          username
          profileImageUrl
          rating
        }
        rank
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

const useGetClubLeaderboard = ({ pageSize }: { pageSize: number }) => {
  const [fetchClubLeaderboardQuery, { data, loading, error }] = useLazyQuery(
    GET_CLUB_LEADERBOARD,
    {
      fetchPolicy: 'cache-and-network',
      notifyOnNetworkStatusChange: true,
    },
  );

  const fetchClubLeaderboard = useCallback(
    async ({ clubId, pageNumber }: { clubId: string; pageNumber: number }) => {
      if (loading) {
        return;
      }

      return fetchClubLeaderboardQuery({
        variables: {
          clubId,
          page: pageNumber,
          pageSize,
        },
      });
    },
    [fetchClubLeaderboardQuery, pageSize],
  );

  return {
    fetchClubLeaderboard,
    loading,
    error,
    data,
  };
};

export default useGetClubLeaderboard;
