import { StyleSheet } from 'react-native';
import DarkTheme from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 100,
    padding: 28,
  },
  contentContainer: {
    width: '100%',
    minHeight: 160,
    gap: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: -10,
  },
  cardContainer: {
    marginTop: -30,
    maxHeight: 120,
    alignItems: 'center',
  },
  opponentFoundText: {
    fontSize: 20,
    fontFamily: 'Montserrat-700',
    color: 'green',
    marginBottom: 10,
  },
  abortSearchingButtonContainer: {
    marginHorizontal: 12,
    marginTop: 72,
  },
  abortSearchingButtonLabel: {
    color: DarkTheme.colors.textDark,
    fontSize: 16,
    fontFamily: 'Montserrat-500',
  },
  cancelLabel: {
    color: DarkTheme.colors.textLight,
  },
  cancelButton: {
    backgroundColor: DarkTheme.colors.primary,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255, 0.2)',
    height: 42,
    width: 140,
  },
  cancelBackground: {
    backgroundColor: DarkTheme.colors.textLight,
    opacity: 0.2,
  },
});

export default styles;
