import { gql, useMutation } from '@apollo/client'
import { GAME_FRAGMENT } from '@/src/core/graphql/fragments/game'
import { useCallback } from 'react'

const SUBMIT_ANSWER_QUERY = gql`
    ${GAME_FRAGMENT}
    mutation SubmitAnswer($answerInput: SubmitAnswerInput) {
        submitAnswer(answerInput: $answerInput) {
            ...CoreGameFields
        }
    }
`

const useSubmitAnswersQuery = () => {
    const [submitAnswerQuery] = useMutation(SUBMIT_ANSWER_QUERY)

    const submitAnswer = useCallback(
        ({
            gameId,
            questionId,
            submittedValue,
            timeOfSubmission,
            isCorrect,
        }) => {
            return submitAnswerQuery({
                variables: {
                    answerInput: {
                        gameId,
                        questionId,
                        submittedValue,
                        timeOfSubmission,
                        isCorrect,
                    },
                },
            })
        },
        [submitAnswerQuery]
    )

    return {
        submitAnswer,
    }
}

export default useSubmitAnswersQuery
