import _mapValues from 'lodash/mapValues';
import _mean from 'lodash/mean';
import _groupBy from 'lodash/groupBy';
import _map from 'lodash/map';
import _max from 'lodash/max';
import _min from 'lodash/min';
import _take from 'lodash/take';
import _sortBy from 'lodash/sortBy';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _concat from 'lodash/concat';
import { ABILITY_QUESTION_CATEGORY } from 'core/constants/questionCategories';

const decodeMinifiedQuestions = ({ minifiedQuestions }) => {
  const allSubmissions = _concat(
    ..._map(minifiedQuestions, (questionString) => {
      const [questionBase, ...submissionsData] = questionString.split('$$');
      const [expression] = questionBase.split('__');

      return _map(submissionsData, (submissionStr) => {
        const [userId, timeTaken] = submissionStr.split('_');
        return {
          userId,
          timeTaken: Math.floor(parseInt(timeTaken)),
          expression,
        };
      });
    }),
  );

  const userSubmissions = _groupBy(allSubmissions, 'userId');

  const gameStats = {
    avgTime: _mapValues(userSubmissions, (submissions) =>
      _mean(_map(submissions, 'timeTaken')),
    ),
    fastestTime: _mapValues(userSubmissions, (submissions) =>
      _min(_map(submissions, 'timeTaken')),
    ),
    slowestTime: _mapValues(userSubmissions, (submissions) =>
      _max(_map(submissions, 'timeTaken')),
    ),
    fastestTop3Avg: _mapValues(userSubmissions, (submissions) => {
      const sortedTimes = _sortBy(_map(submissions, 'timeTaken'));
      return _mean(_take(sortedTimes, 3));
    }),
    slowestTop3Avg: _mapValues(userSubmissions, (submissions) => {
      const sortedTimes = _sortBy(_map(submissions, 'timeTaken'));
      return _mean(
        _take(
          _sortBy(sortedTimes, (t) => -t),
          3,
        ),
      );
    }),
    totalSubmissions: _mapValues(userSubmissions, 'length'),

    submissionTimes: _mapValues(userSubmissions, (submissions) =>
      _map(submissions, 'timeTaken'),
    ),

    add: calculateOperationStats(allSubmissions, '+'),
    subtraction: calculateOperationStats(allSubmissions, '-'),
    multiplication: calculateOperationStats(allSubmissions, '×'),
    divison: calculateOperationStats(allSubmissions, '÷'),

    mod: calculateOperationStats(allSubmissions, ABILITY_QUESTION_CATEGORY.MOD),
    nthRoot: calculateOperationStats(allSubmissions, '✓'),
    exponent: calculateOperationStats(allSubmissions, '^'),
    lcm: calculateOperationStats(allSubmissions, ABILITY_QUESTION_CATEGORY.LCM),
    hcf: calculateOperationStats(allSubmissions, ABILITY_QUESTION_CATEGORY.HCF),
    primeFactorization: calculateOperationStats(allSubmissions, ''), // Check with a different identifier
    sumOfSquares: calculateOperationStats(allSubmissions, '='), // Check with a different identifier
    multipleOperators: calculateOperationStats(allSubmissions, '$'),

    questions: _map(minifiedQuestions, (questionString, questionIndex) => {
      const [questionBase, ...submissionsData] = questionString.split('$$');
      const [expression, presetIdentifier] = questionBase.split('__');

      return {
        questionIndex,
        expression,
        presetIdentifier,
      };
    }),
  };

  return gameStats;
};

const calculateOperationStats = (submissions, operator) => {
  const operationSubmissions = _filter(submissions, (submission) =>
    _includes(submission.expression, operator),
  );

  return _mapValues(
    _groupBy(operationSubmissions, 'userId'),
    (userSubmissions) => _mean(_map(userSubmissions, 'timeTaken')),
  );
};

const useGetGameStats = ({ minifiedQuestions }) =>
  decodeMinifiedQuestions({ minifiedQuestions });

export default useGetGameStats;
