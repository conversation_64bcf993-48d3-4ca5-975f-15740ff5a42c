import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _toString from 'lodash/toString';

const ACCEPT_REMATCH_MUTATION = gql`
  mutation cancelRematch($gameId: ID!) {
    cancelRematchRequest(gameId: $gameId)
  }
`;

const useCancelRematchRequest = () => {
  const [cancelRematchQuery] = useMutation(ACCEPT_REMATCH_MUTATION);

  const cancelRematchRequest = useCallback(
    ({ gameId }) =>
      cancelRematchQuery({
        variables: {
          gameId: _toString(gameId),
        },
      }),
    [cancelRematchQuery],
  );

  return {
    cancelRematchRequest,
  };
};

export default useCancelRematchRequest;
