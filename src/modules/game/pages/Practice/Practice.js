import React, { useCallback, useMemo, useState } from 'react';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import _toNumber from 'lodash/toNumber';
import _isEmpty from 'lodash/isEmpty';
import { useRouter } from 'expo-router';
import useUserActivityTracker from 'core/hooks/useUserActivityTracker';
import ACTIVITY_TYPES from '@/src/core/constants/activityTypes';
import QuestionsRenderer from 'shared/QuestionsRenderer';
import _split from 'lodash/split';
import { PRESET_CATEGORY } from 'core/constants/presets';
import Header from './Header';
import Footer from '../PlayGame/Footer';
import usePracticeGameQuestionsState from '../../hooks/usePracticeGameQuestionsState';
import PracticeGameResult from '../PracticeGameResult';

import Loading from '../../../../components/atoms/Loading';
import styles from './Practice.style';
import getCurrentTimeWithOffset from '../../../../core/utils/getCurrentTimeWithOffset';
import useGameWaitingTimer from '../../../../components/shared/game/hooks/useGameWaitingTimer';

const PracticeGame = ({ game }) => {
  const { startTime, config } = game;
  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTimeWithOffset();

  const { renderQuestionOverlay } = useGameWaitingTimer({ game });

  const { timeLimit } = config;
  const router = useRouter();

  const hasGameEnded =
    startTimeDate.getTime() + _toNumber(timeLimit) * 1000 <= currentTime;
  const [gameEnded, setGameEnded] = useState(hasGameEnded);
  const { isMobile } = useMediaQuery();

  const { currentQuestion, playerScore, submitAnswer } =
    usePracticeGameQuestionsState();

  const { updateActivity } = useUserActivityTracker();

  const onGameEnded = useCallback(() => {
    updateActivity({
      activityType: ACTIVITY_TYPES.PRACTICE_ONLINE,
      duration: _toNumber(timeLimit) * 1000,
    });
    setGameEnded(true);
  }, [updateActivity, timeLimit]);

  const shouldRenderInputInFooter = useMemo(() => {
    const { presetIdentifier } = currentQuestion ?? EMPTY_OBJECT;
    if (_isEmpty(presetIdentifier)) {
      return true;
    }
    const isFractionQuestion =
      _split(presetIdentifier, '_')?.[0] === PRESET_CATEGORY.FRAC;
    return !isFractionQuestion;
  }, [currentQuestion]);

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer question={currentQuestion} submitAnswer={submitAnswer} />
      </View>
    ),
    [currentQuestion, submitAnswer],
  );

  if (gameEnded) {
    return <PracticeGameResult playerScore={playerScore} />;
  }

  if (_isEmpty(currentQuestion)) {
    return <Loading />;
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View style={styles.mainContainer}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Header playerScore={playerScore} onGameEnded={onGameEnded} />
          </View>
          <View style={[styles.question]}>
            <QuestionsRenderer
              question={currentQuestion}
              renderQuestionOverlay={renderQuestionOverlay}
            />
          </View>
          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </KeyboardAvoidingView>
  );
};

export default React.memo(PracticeGame);
