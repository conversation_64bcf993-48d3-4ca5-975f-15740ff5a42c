import React from 'react';
import _toNumber from 'lodash/toNumber';
import useGameContext from '../../hooks/useGameContext';
import Loading from '../../../../components/atoms/Loading';
import { GAME_STATUS } from '../../constants/game';
import GameErrorPage from '../GameErrorPage';
import ShowdownLeaderBoard from '../ShowdownLeaderboard/ShowdownLeaderBoard';

const ShowdownGameEnd = () => {
  const { game } = useGameContext();
  const { _id: gameId, startTime, config, gameStatus } = game;
  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTime();

  const { timeLimit } = config;
  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000 - 500;

  const hasGameTimeLimitOver = endTime <= currentTime;

  if (gameStatus === GAME_STATUS.ENDED) {
    return <ShowdownLeaderBoard />;
  }

  const isEndingGame =
    hasGameTimeLimitOver && gameStatus === GAME_STATUS.STARTED;

  if (isEndingGame) {
    return <Loading />;
  }

  return <GameErrorPage />;
};

export default React.memo(ShowdownGameEnd);
