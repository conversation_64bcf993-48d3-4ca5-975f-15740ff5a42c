import { StyleSheet } from "react-native";
import useMediaQuery from 'core/hooks/useMediaQuery'
import dark from "../../../../../core/constants/themes/dark";
import { useMemo } from "react";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flexDirection: 'row',
        gap: 10
    },
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 3
    },
    requestCard: {
        borderColor: dark.colors.tertiary,
        borderWidth: 2,
        borderRadius: 10,
        paddingHorizontal: 12,
        paddingVertical: 12,
        minWidth: 328,
        maxWidth: '90%',
        backgroundColor: dark.colors.background,
        marginHorizontal: isCompactMode ? 16 : 0
    },
    acceptText: {
        fontSize: 14,
        fontFamily: 'Montserrat-600',
        width:100,
        color: dark.colors.success,
    },
    rejectText: {
        fontSize: 14,
        fontFamily: 'Montserrat-600',
        color: dark.colors.error,
    },
    buttonContainer :{ 
        flexDirection: 'row',
        gap: 10, 
        justifyContent: 'flex-end',
        marginTop: 10 
    },
    image:{ 
        borderWidth: 2, 
        borderColor: dark.colors.offWhite, 
        width: 34, 
        height: 34, 
        borderRadius: 5, 
        overflow: 'hidden' 
    }
})


const useRequestCardStyles = () => {
    const { isMobile } = useMediaQuery();

    const styles = useMemo(() => createStyles(isMobile), [isMobile]);

    return styles;
};

export default useRequestCardStyles
