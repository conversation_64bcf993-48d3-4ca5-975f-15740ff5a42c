import React from 'react';
import { View } from 'react-native';
import PropTypes from 'prop-types';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import dark from 'core/constants/themes/dark';
import useGameResultFooterStyles from './GameResultFooter.style';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';

const GameResultFooter = ({
  rematchWithSamePlayer,
  navigateToNewGame,
}) => {
  const styles = useGameResultFooterStyles();
  const { isMobile } = useMediaQuery();

  return (
    <View style={[styles.footer, !isMobile && { height: 40 }]}>
      <View style={styles.footerContainer}>
        <InteractivePrimaryButton
          label="REMATCH"
          labelStyle={styles.bottomButtonLabel}
          buttonStyle={[
            styles.bottomButtonStyle,
            { borderColor: dark.colors.metricsBackgroundColor },
          ]}
          buttonBorderBackgroundStyle={[
            styles.bottomButtonBackgroundStyle,
            { backgroundColor: dark.colors.metricsBackgroundColor },
          ]}
          onPress={rematchWithSamePlayer}
        />
        <InteractivePrimaryButton
          label="NEW GAME"
          labelStyle={styles.bottomButtonLabel}
          buttonStyle={styles.bottomButtonStyle}
          buttonBorderBackgroundStyle={styles.bottomButtonBackgroundStyle}
          onPress={navigateToNewGame}
        />
      </View>
    </View>
  );
};

GameResultFooter.propTypes = {
  rematchWithSamePlayer: PropTypes.func.isRequired,
  navigateToNewGame: PropTypes.func.isRequired,
};

export default React.memo(GameResultFooter);