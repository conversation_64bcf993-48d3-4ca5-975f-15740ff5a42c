import { Platform, StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const iconSize = Platform.select({
  web: 16,
  default: 24,
});

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    height: 60,
    justifyContent: 'space-between',
    marginTop: 0,
    flexDirection: 'row',
  },
  homeButton: {
    backgroundColor: dark.colors.primary,
    borderWidth: 1.5,
    height: 42,
    width: 42,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
  },
  homebuttonBackground: {
    backgroundColor: dark.colors.textLight,
    opacity: 0.2,
    borderRadius: 12,
  },
  imageContainer: {
    width: 150,
    height: 40,
  },
  image: {
    width: '100%',
    height: '100%',
  },
});

export default styles;
