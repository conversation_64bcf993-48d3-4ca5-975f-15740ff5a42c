import React from 'react';
import { View, Text } from 'react-native';
import PlayerC<PERSON> from '../PlayerCard';
import useGameResultPlayersStyles from './GameResultPlayers.style';
import dark from 'core/constants/themes/dark';

const GameResultPlayers = ({ adaptedPlayers, player1, player2, isCurrPlayerWinner, gameType, isFlashAnzan }) => {
  const styles = useGameResultPlayersStyles();


  const renderPlayer = ({ player, gameType }) => {
    return (
      <View style={styles.playerContainer}>
        <PlayerCard user={player} gameType={gameType} />
      </View>
    );
  };

  return (
    <View style={styles.resultsContainer}>
      {renderPlayer({ player: adaptedPlayers[0], gameType: gameType })}
      <View style={styles.scoresContainer}>
        {isFlashAnzan ? <View style={styles.digitScoreContainer}>
          <Text style={styles.scoreText}>{player1?.isWinner ? 1 : 0}</Text>
          <Text style={styles.scoreText}>{player2?.isWinner ? 1 : 0}</Text>
        </View>: <View></View>}
        <View style={styles.totalScoreContainer}>
          <View
            style={[
              styles.totalScore,
              isCurrPlayerWinner
                ? { borderColor: dark.colors.victoryColor }
                : { borderColor: dark.colors.tertiary },
            ]}
          >
            <Text style={styles.innerScoreText}>{adaptedPlayers[0].score}</Text>
          </View>
          <Text style={styles.hyphen}>-</Text>
          <View
            style={[
              styles.totalScore,
              !isCurrPlayerWinner
                ? { borderColor: dark.colors.defeatColor }
                : { borderColor: dark.colors.tertiary },
            ]}
          >
            <Text style={styles.innerScoreText}>{adaptedPlayers[1].score}</Text>
          </View>
        </View>
      </View>
      {renderPlayer({ player: adaptedPlayers[1], gameType: gameType })}
    </View>
  );
};

export default GameResultPlayers;