import dark from 'core/constants/themes/dark';
import React from 'react';
import _size from 'lodash/size';
import _map from 'lodash/map';
import {ScrollView, Text, View} from 'react-native';
import {processGroupPlayPlayersData} from '@/src/modules/game/utils/processGroupPlayPlayersData';
import GroupPlayLeaderboardRow from 'modules/game/pages/GroupPlayResult/components/GroupPlayLeaderboardRow';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useGameContext from '../../../hooks/useGameContext';
import styles from '../../GroupPlayResult/GroupPlayResult.style';
import useGroupPlayUserScore from '../../../hooks/useGroupPlayUserScore';

const LeaderBoardInBetweenGame = ({currentQuestionId}) => {
  const {players, game} = useGameContext();

  const {getUserCurrentQuestionScore} = useGroupPlayUserScore({game});

  const {isMobile: isCompactMode} = useMediaQuery();

  const {rankedPlayers} =
  processGroupPlayPlayersData({players, game}) ?? EMPTY_OBJECT;

  const renderGroupPlayLeaderboardRow = (player, index) => {
    const {hasSolved, score} = getUserCurrentQuestionScore({
      playerId: player?._id,
      questionId: currentQuestionId,
    });
    return (
      <View
        key={`${index}`}
        style={[
          {
            paddingVertical: 10,
            paddingHorizontal: 16,
            borderBottomColor: dark.colors.tertiary,
            borderBottomWidth: 1,
          },
          hasSolved && {
            backgroundColor: '#263624',
          },
        ]}
      >
        <GroupPlayLeaderboardRow
          score={player?.score}
          currentQuesScore={hasSolved ? score : undefined}
          user={player}
          rank={index + 1}
        />
      </View>
    )
  }

  if (isCompactMode) {
    return null;
  }

  return (
    <View
      style={{
        height: '100%',
        width: 300,
        backgroundColor: dark.colors.backgroundColor,
        flex: 1,
      }}
    >
      <View
        style={{
          flex: 1,
          backgroundColor: dark.colors.gradientBackground,
          borderRadius: 12,
          marginLeft: 16,
          marginVertical: 16,
        }}
      >
        {_size(rankedPlayers) > 0 && (
          <View
            style={{
              flexDirection: 'row',
              marginTop: 15,
              paddingHorizontal: 16,
            }}
          >
            <Text
              style={[
                styles.rowHeadingText,
                styles.rankHeader,
                !isCompactMode && {flex: 0.2},
              ]}
            >
              #
            </Text>
            <Text style={[styles.rowHeadingText, styles.userInfo]}>
              Mathlete
            </Text>
            <Text
              style={[
                styles.rowHeadingText,
                {textAlign: 'right'},
                styles.scoreDetail,
              ]}
            >
              Score
            </Text>
          </View>
        )}
        <ScrollView showsVerticalScrollIndicator={false}>
          {_map(rankedPlayers, renderGroupPlayLeaderboardRow)}
        </ScrollView>
      </View>
    </View>
  );
};

export default React.memo(LeaderBoardInBetweenGame);
