import {StyleSheet} from "react-native";
import dark from "core/constants/themes/dark";

const styles = StyleSheet.create({
  image: {
    flex: 1,
    width: '100%',
    maxWidth: 400,
    maxHeight: 400,
    height: '100%',
    resizeMode: 'cover',
    justifyContent: 'center',
  },
  questionExpression: {
    fontSize: 24,
    lineHeight: 29,
    fontFamily: 'Montserrat-500',
    color: 'white',
  },
  expressionContainer: {
    gap: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  expressionRow: {
    flexDirection: 'row',
    gap: 8,
    height: 32,
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: 120,
    paddingRight: 36,
  },
  operatorContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  operator: {
    width: 24,
    minWidth: 20,
    maxWidth: 24,
    fontSize: 24,
    lineHeight: 29,
    textAlign: 'right',
    color: 'white',
  },
  numberContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  timerText: {
    fontSize: 20,
    color: 'white',
  },
  time: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    color: dark.colors.stroke,
  },
})

export default styles;
