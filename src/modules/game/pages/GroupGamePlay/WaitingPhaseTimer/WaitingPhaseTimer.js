import React, {useEffect, useState} from 'react';
import {Text, View} from 'react-native';
import dark from "core/constants/themes/dark";
import Dark from "core/constants/themes/dark";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

const WaitingPhaseTimer = () => {
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 1;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <View style={{flexDirection: 'row', gap: 4}}>
      <Text style={{
        fontFamily: "Montserrat-600",
        fontSize: 18,
        color: dark.colors.textDark,
        textAlign: 'center',
      }}>
        {`Next question in`}
      </Text>
      <MaterialIcons name={'timer'} color={Dark.colors.textDark} size={20}/>
      <Text style={{
        fontFamily: "Montserrat-600",
        fontSize: 18,
        color: dark.colors.textDark,
        textAlign: 'center',
      }}>{countdown}</Text>
    </View>
  );

}

export default React.memo(WaitingPhaseTimer);