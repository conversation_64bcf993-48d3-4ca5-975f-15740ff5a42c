import React, { useCallback, useMemo, useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import PropTypes from 'prop-types';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import _findIndex from 'lodash/findIndex';
import _size from 'lodash/size';
import Header from 'shared/Header';
import dark from 'core/constants/themes/dark';
import PrimaryButton from 'atoms/PrimaryButton';
import { GAME_TYPES } from '@/src/core/constants/gameTypes';
import useGetGameDetailedAnalysis from '../../hooks/queries/useGetGameDetailedAnalysis';
import useGameQuestionsDetailedAnalysisStyles from './GameQuestionsDetailedAnalysis.style';
import Question from '../PlayGame/Question';
import { useSession } from '../../../auth/containers/AuthProvider';
import { isFaster } from '../GameAnalysis/components/GameStatsRow/GameStatsRow';
import { getDescriptiveNameFromIdentifier } from '../../../practice/utils/getIdentifierStringFromConfig';
import {
  getComplementTextString,
  getValueText,
} from '../../utils/getComplementText';
import handlePracticePresetByIdentifier from '../../../../core/utils/handlePracticePresetByIdentifier';
import WebBackButton from '../../../../components/shared/WebBackButton';
import AbilityQuestion from '../PlayGame/AbilityQuestion/AbilityQuestion';

const GameQuestionsDetailedAnalysis = (props) => {
  const { questionsWithAnalytics, game } = props;

  const { userId: currUserId } = useSession();
  const opponentUserId = useMemo(() => {
    const { players } = game ?? EMPTY_OBJECT;
    const index = _findIndex(
      players,
      (player) => player?.userId !== currUserId,
    );

    return index != -1 ? players[index]?.userId : '';
  }, [game, currUserId]);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  const styles = useGameQuestionsDetailedAnalysisStyles();

  const { gameType } = game;

  const renderQueWithAnalytics = useCallback(() => {
    const { question, avgTimes, globalAvgTime, globalBestTime } =
      questionsWithAnalytics[currentQuestionIndex];
    const indexOfCurrUserScore = _findIndex(
      avgTimes,
      (avgTimeField) => avgTimeField?.userId === currUserId,
    );
    const indexOfOpponentUserScore = _findIndex(
      avgTimes,
      (avgTimeField) => avgTimeField?.userId === opponentUserId,
    );
    const currUserScores =
      indexOfCurrUserScore !== -1 ? avgTimes[indexOfCurrUserScore] : 0;
    const opponentScores =
      indexOfOpponentUserScore !== -1 ? avgTimes[indexOfOpponentUserScore] : 0;
    const isCurrUserFaster = isFaster({
      userScore: currUserScores?.questionAvgTime,
      opponentScore: opponentScores?.questionAvgTime,
    });
    const complementText = getComplementTextString({
      currUserScores,
      opponentScores,
      globalBestTime,
      globalAvgTime,
    });

    return (
      <>
        <Text style={styles.currQueIndexText}>
          {currentQuestionIndex + 1} of {_size(questionsWithAnalytics)}
        </Text>
        <View style={styles.questionsWithAnalytics}>
          <View style={styles.question}>
            {gameType === GAME_TYPES.ABILITY_DUELS ? (
              <AbilityQuestion question={question} />
            ) : (
              <Question question={question} />
            )}
          </View>
          <View style={styles.tableContainer}>
            <View style={styles.tableHeader}>
              <Text style={styles.headerCell}>Metrics</Text>
              <Text style={styles.headerCell}>Your</Text>
              <Text style={styles.headerCell}>Opponent</Text>
              <Text style={styles.headerCell}>Global</Text>
            </View>

            <View style={styles.tableRow}>
              <Text style={styles.metricCell}>Time Taken</Text>
              <Text
                style={[
                  styles.valueCell,
                  isCurrUserFaster && { color: dark.colors.secondary },
                ]}
              >
                {getValueText(currUserScores?.questionAvgTime)}
              </Text>
              <Text
                style={[
                  styles.valueCell,
                  !isCurrUserFaster && { color: dark.colors.secondary },
                ]}
              >
                {getValueText(opponentScores?.questionAvgTime)}
              </Text>
              <Text style={styles.valueCell}>-</Text>
            </View>

            <View style={styles.tableRow}>
              <Text style={styles.metricCell}>Avg Time of Preset</Text>
              <Text style={styles.valueCell}>
                {getValueText(currUserScores?.presetAvgTime)}
              </Text>
              <Text style={styles.valueCell}>
                {getValueText(opponentScores?.presetAvgTime)}
              </Text>
              <Text style={styles.valueCell}>
                {getValueText(globalAvgTime)}
              </Text>
            </View>

            <View style={[styles.tableRow, { borderBottomWidth: 0 }]}>
              <Text style={styles.metricCell}>Best Time of Preset</Text>
              <Text style={styles.valueCell}>
                {getValueText(currUserScores?.presetBestTime)}
              </Text>
              <Text style={styles.valueCell}>
                {getValueText(opponentScores?.presetBestTime)}
              </Text>
              <Text style={styles.valueCell}>
                {getValueText(globalBestTime)}
              </Text>
            </View>
          </View>
        </View>
        <Text style={styles.complementText}>{complementText}</Text>
      </>
    );
  }, [
    gameType,
    questionsWithAnalytics,
    currentQuestionIndex,
    opponentUserId,
    currUserId,
  ]);

  const shouldShowPracticeButton = gameType !== GAME_TYPES.ABILITY_DUELS;

  return (
    <View style={styles.container}>
      <Header title="Game Analysis" />
      <ScrollView
        contentContainerStyle={styles.innerContainer}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <WebBackButton />
        {renderQueWithAnalytics()}
        <View
          style={[
            styles.buttonRow,
            !shouldShowPracticeButton && { marginBottom: 0 },
          ]}
        >
          <TouchableOpacity
            onPress={() =>
              currentQuestionIndex != 0
                ? setCurrentQuestionIndex((prev) => prev - 1)
                : null
            }
          >
            <Text
              style={{
                fontSize: 13,
                fontFamily: 'Montserrat-600',
                color:
                  currentQuestionIndex == 0
                    ? dark.colors.tertiary
                    : dark.colors.secondary,
              }}
            >
              Previous
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() =>
              currentQuestionIndex < _size(questionsWithAnalytics) - 1
                ? setCurrentQuestionIndex((prev) => prev + 1)
                : null
            }
          >
            <Text
              style={{
                fontSize: 13,
                fontFamily: 'Montserrat-600',
                color:
                  currentQuestionIndex == _size(questionsWithAnalytics) - 1
                    ? dark.colors.tertiary
                    : dark.colors.secondary,
              }}
            >
              Next
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {shouldShowPracticeButton && (
        <View style={styles.complementsRow}>
          <Text style={styles.presetIdentifierText}>
            {getDescriptiveNameFromIdentifier({
              identifier:
                questionsWithAnalytics[currentQuestionIndex]?.question
                  ?.presetIdentifier,
            })}
          </Text>
          <PrimaryButton
            label="Practice This Preset"
            onPress={() => {
              handlePracticePresetByIdentifier({
                identifier:
                  questionsWithAnalytics[currentQuestionIndex]?.question
                    ?.presetIdentifier,
              });
            }}
            radius={20}
            buttonStyle={{ height: 40, paddingHorizontal: 24 }}
          />
        </View>
      )}
    </View>
  );
};

GameQuestionsDetailedAnalysis.propTypes = {
  questionsWithAnalytics: PropTypes.array,
  game: PropTypes.object,
};

const GameQuestionsDetailedAnalysisContainer = (props) => {
  const { gameId } = props;

  const { gameAnalysis, loading, error } = useGetGameDetailedAnalysis({
    gameId,
  });

  const { questions: questionsWithAnalytics, game } =
    gameAnalysis ?? EMPTY_OBJECT;

  if (loading) {
    return <Loading label="Loading Detailed Analysis" />;
  }

  if (error) {
    return <ErrorView errorMessage="Something went wrong " />;
  }

  return (
    <GameQuestionsDetailedAnalysis
      game={game}
      questionsWithAnalytics={questionsWithAnalytics}
    />
  );
};

GameQuestionsDetailedAnalysisContainer.propTypes = {
  gameId: PropTypes.string,
};

export default React.memo(GameQuestionsDetailedAnalysisContainer);
