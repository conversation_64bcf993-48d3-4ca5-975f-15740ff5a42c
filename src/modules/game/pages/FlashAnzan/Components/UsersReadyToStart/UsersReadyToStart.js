import React, { useCallback, useMemo } from 'react'
import { View } from 'react-native'
import StartGame from '../../../StartGame'
import WaitingForGameCreatorToStart from '../../../WaitingToStart'
import { GAME_STATUS } from '../../../../constants/game'
import _size from 'lodash/size'
import { useSession } from '@/src/modules/auth/containers/AuthProvider'
import { useRouter } from 'expo-router'
import { Text, Button } from '@rneui/themed'
import styles from './UsersReadyToStart.style'
import GameLobbyPlayerCards from '../../../../components/GameLobbyPlayerCards'
import useHandleLeaveGame from '../../../../hooks/useHandleLeaveGame'

const UsersReadyToStart = (props) => {
    const { game } = props

    const { players, createdBy, gameStatus, _id: gameId } = game

    const router = useRouter()

    const { userId } = useSession()

    const isGameOwner = useMemo(() =>
        gameStatus === GAME_STATUS.READY && _size(players) === 2 && userId === createdBy
        , [gameStatus, players, createdBy, userId])

    const { onPressLeaveGame, isCancellingGame } = useHandleLeaveGame({ gameId })


    return (
        <View style={styles.container}>
            <View style={styles.container}>
                <View style={styles.contentContainer}>
                    <GameLobbyPlayerCards game={game} />
                    {isGameOwner ? (
                        <StartGame game={game} />
                    ) : (
                        <WaitingForGameCreatorToStart game={game} />
                    )}
                </View>
            </View>
            <Button type="clear" onPress={onPressLeaveGame} >
                <Text style={styles.leaveGameStyle}>Leave game</Text>
            </Button>
        </View>
    )
}

export const UsersReadyToStartContainer = props => {
    const { game } = props;
    const { gameStatus } = game;

    if (gameStatus !== GAME_STATUS.READY) {
        return null
    }

    return <UsersReadyToStart {...props} />
}

export default UsersReadyToStartContainer
