import React, { useCallback, useMemo, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import { LineChart } from 'react-native-chart-kit';
import PropTypes from 'prop-types';
import _map from 'lodash/map';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { useRouter } from 'expo-router';
import _size from 'lodash/size';
import useGameAnalysisGraphStyles from './GameAnalysisGraph.style';

const GameAnalysisGraph = (props) => {
  const { userSubmissionTimes, opponentSubmissionTimes, gameId } = props;
  const router = useRouter();
  const styles = useGameAnalysisGraphStyles();
  const [graphWidth, setGraphWidth] = useState(300);

  const onGraphLayout = useCallback((event) => {
    const { width } = event.nativeEvent.layout;
    setGraphWidth(width);
  }, []);

  const navigateToDetailedAnalysis = useCallback(() => {
    router.push(`/game/${gameId}/analysis`);
  }, [gameId, router]);

  const chartConfig = {
    backgroundColor: dark.colors.gradientBackground,
    backgroundGradientFrom: dark.colors.gradientBackground,
    backgroundGradientFromOpacity: 0,
    backgroundGradientTo: dark.colors.gradientBackground,
    backgroundGradientToOpacity: 0.5,
    decimalPlaces: 2,
    color: () => 'white',
    labelColor: () => 'white',
    strokeWidth: 2,
    style: { borderRadius: 16, fontFamily: 'Montserrat-600' },
    propsForDots: {
      r: '3',
      stroke: 'white',
      strokeWidth: '0',
    },
    propsForBackgroundLines: { strokeDasharray: '' },
    fillShadowGradientFrom: dark.colors.gradientBackground,
    fillShadowGradientTo: dark.colors.gradientBackground,
    propsForLabels: { style: { fontFamily: 'Montserrat-600' } },
  };

  const xAxisLabels = useMemo(() => {
    const xAxisSize = Math.max(
      _size(userSubmissionTimes),
      _size(opponentSubmissionTimes),
    );
    return Array.from({ length: xAxisSize }, (_, index) => index + 1);
  }, [userSubmissionTimes, opponentSubmissionTimes]);

  const timeGraphData = {
    labels: _size(xAxisLabels) > 20 ? EMPTY_ARRAY : xAxisLabels,
    legend: ['Yours', 'Opponent'],
    datasets: [
      {
        data: _map(userSubmissionTimes, (val) => val / 1000),
        color: () => dark.colors.secondary,
        strokeWidth: 2,
      },
      {
        data: _map(opponentSubmissionTimes, (val) => val / 1000),
        color: () => dark.colors.graphRedBg,
        strokeWidth: 2,
      },
    ],
  };

  return (
    <View style={styles.section} onLayout={onGraphLayout}>
      <Text style={styles.sectionTitle}> TIME TAKEN PER QUESTION </Text>
      <Text
        style={[
          styles.highlightedText,
          {
            color: 'white',
            textAlign: 'left',
            fontSize: 15,
            lineHeight: 18,
            fontFamily: 'Montserrat-700',
          },
        ]}
      >
        {/* {averageTime} Sec */}
      </Text>
      <View style={styles.detailedAnalysisButtonRow}>
        <TouchableOpacity
          onPress={navigateToDetailedAnalysis}
          style={styles.detailedAnalysisButton}
        >
          <Text style={styles.detailedAnalysisButtonText}>
            Detailed Analysis
          </Text>
          <FontAwesome6
            name="arrow-right-long"
            size={16}
            color={dark.colors.secondary}
          />
        </TouchableOpacity>
      </View>
      <View style={styles.detailedAnalysisButtonRow}>
        <TouchableOpacity
          onPress={navigateToDetailedAnalysis}
          style={styles.detailedAnalysisButton}
        >
          <Text style={styles.detailedAnalysisButtonText}>
            Detailed Analysis
          </Text>
          <FontAwesome6
            name="arrow-right-long"
            size={16}
            color={dark.colors.secondary}
          />
        </TouchableOpacity>
      </View>
      <LineChart
        data={timeGraphData}
        width={graphWidth}
        height={220}
        withHorizontalLines={false}
        withVerticalLines={false}
        withDots
        withShadow={false}
        chartConfig={chartConfig}
        style={styles.chart}
        bezier
      />
    </View>
  );
};

GameAnalysisGraph.propTypes = {
  userSubmissionTimes: PropTypes.array,
  opponentSubmissionTimes: PropTypes.array,
};

export default React.memo(GameAnalysisGraph);
