import React, { useCallback, useMemo, useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import { LineChart } from 'react-native-chart-kit';
import PropTypes from 'prop-types';
import _map from 'lodash/map';
import _chain from 'lodash';
import _startsWith from 'lodash/startsWith';
import _filter from 'lodash/filter';
import _size from 'lodash/size';
import _includes from 'lodash/includes';
import _countBy from 'lodash/countBy';
import useNewGameAnalysisGraphStyles from './NewGameAnalysisGraph.style';

const PRESET_IDENTIFIERS = {
  ALL: 'all',
  ADDSUB: 'ADDSUB',
  ADD: 'ADD',
  MULT: 'MULT',
  DIV: 'DIV',
  HCF: 'HCF',
  LCM: 'LCM',
  ROOT: 'ROOT',
  EXPO: 'EXPO',
  SOS: 'SOS',
  MULOP: 'MULOP',
  PF: 'PF',
  MOD: 'MOD',
};

const NewGameAnalysisGraph = (props) => {
  const { userSubmissionTimes, opponentSubmissionTimes, gameId, questions } =
    props;
  const styles = useNewGameAnalysisGraphStyles();
  const [graphWidth, setGraphWidth] = useState(300);
  const [selectedFilter, setSelectedFilter] = useState(PRESET_IDENTIFIERS.ALL);

  const baseOptions = [
    { id: PRESET_IDENTIFIERS.ALL, label: 'All' },
    { id: PRESET_IDENTIFIERS.ADD, label: 'Addition' },
    { id: PRESET_IDENTIFIERS.ADDSUB, label: 'Subtraction' },
    { id: PRESET_IDENTIFIERS.MULT, label: 'Multiplication' },
    { id: PRESET_IDENTIFIERS.DIV, label: 'Division' },
    { id: PRESET_IDENTIFIERS.HCF, label: 'HCF' },
    { id: PRESET_IDENTIFIERS.LCM, label: 'LCM' },
    { id: PRESET_IDENTIFIERS.ROOT, label: 'Root' },
    { id: PRESET_IDENTIFIERS.EXPO, label: 'Exponent' },
    { id: PRESET_IDENTIFIERS.SOS, label: 'Sum of Squares' },
    { id: PRESET_IDENTIFIERS.MULOP, label: 'Multi Operators' },
    { id: PRESET_IDENTIFIERS.PF, label: 'Prime Factorization' },
    { id: PRESET_IDENTIFIERS.MOD, label: 'Mod' },
  ];

  const questionCounts = useMemo(() => {
    const counts = _countBy(questions, (questions) => {
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.ADDSUB))
        return PRESET_IDENTIFIERS.ADDSUB;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.ADD))
        return PRESET_IDENTIFIERS.ADD;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.MULT))
        return PRESET_IDENTIFIERS.MULT;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.DIV))
        return PRESET_IDENTIFIERS.DIV;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.HCF))
        return PRESET_IDENTIFIERS.HCF;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.LCM))
        return PRESET_IDENTIFIERS.LCM;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.ROOT))
        return PRESET_IDENTIFIERS.ROOT;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.EXPO))
        return PRESET_IDENTIFIERS.EXPO;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.SOS))
        return PRESET_IDENTIFIERS.SOS;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.MULOP))
        return PRESET_IDENTIFIERS.MULOP;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.PF))
        return PRESET_IDENTIFIERS.PF;
      if (_startsWith(questions.presetIdentifier, PRESET_IDENTIFIERS.MOD))
        return PRESET_IDENTIFIERS.MOD;
    });
    return counts;
  }, [questions]);

  const availableFilterOptions = useMemo(
    () =>
      _filter(
        baseOptions,
        (option) =>
          option.id === PRESET_IDENTIFIERS.ALL ||
          (questionCounts[option.id] && questionCounts[option.id] > 0),
      ),
    [questionCounts],
  );

  const filteredData = useMemo(() => {
    if (selectedFilter === PRESET_IDENTIFIERS.ALL) {
      return {
        userTimes: userSubmissionTimes,
        opponentTimes: opponentSubmissionTimes,
        filteredQuestions: questions,
      };
    }

    const filteredIndices = _chain(questions)
      .filter((q) => _startsWith(q.presetIdentifier, selectedFilter))
      .map('questionIndex')
      .value();

    return {
      userTimes: _filter(userSubmissionTimes, (_, idx) =>
        _includes(filteredIndices, idx),
      ),
      opponentTimes: _filter(opponentSubmissionTimes, (_, idx) =>
        _includes(filteredIndices, idx),
      ),
      filteredQuestions: _filter(questions, (_, idx) =>
        _includes(filteredIndices, idx),
      ),
    };
  }, [selectedFilter, questions, userSubmissionTimes, opponentSubmissionTimes]);

  const onGraphLayout = useCallback((event) => {
    const { width } = event.nativeEvent.layout;
    setGraphWidth(width);
  }, []);

  const chartConfig = {
    backgroundColor: dark.colors.gradientBackground,
    backgroundGradientFrom: dark.colors.gradientBackground,
    backgroundGradientFromOpacity: 0,
    backgroundGradientTo: dark.colors.gradientBackground,
    backgroundGradientToOpacity: 0.5,
    decimalPlaces: 2,
    color: () => 'white',
    labelColor: () => 'white',
    strokeWidth: 2,
    style: { borderRadius: 16, fontFamily: 'Montserrat-600' },
    propsForDots: {
      r: '3',
      stroke: 'white',
      strokeWidth: '0',
    },
    propsForBackgroundLines: { strokeDasharray: '' },
    fillShadowGradientFrom: dark.colors.gradientBackground,
    fillShadowGradientTo: dark.colors.gradientBackground,
    propsForLabels: { style: { fontFamily: 'Montserrat-600' } },
  };

  const xAxisLabels = useMemo(() => {
    const xAxisSize = Math.max(
      _size(userSubmissionTimes),
      _size(opponentSubmissionTimes),
    );
    return Array.from({ length: xAxisSize }, (_, index) => index + 1);
  }, [userSubmissionTimes, opponentSubmissionTimes]);

  const timeGraphData = {
    labels: _size(xAxisLabels) > 20 ? EMPTY_ARRAY : xAxisLabels,
    legend: ['Yours', 'Opponent'],
    datasets: [
      {
        data: _map(filteredData.userTimes, (val) => val / 1000),
        color: () => dark.colors.secondary,
        strokeWidth: 2,
      },
      {
        data: _map(filteredData.opponentTimes, (val) => val / 1000),
        color: () => dark.colors.graphRedBg,
        strokeWidth: 2,
      },
    ],
  };

  return (
    <View style={styles.section} onLayout={onGraphLayout}>
      <Text style={styles.sectionTitle}> TIME TAKEN PER QUESTION </Text>

      <Text
        style={[
          styles.highlightedText,
          {
            color: 'white',
            textAlign: 'left',
            fontSize: 15,
            lineHeight: 18,
            fontFamily: 'Montserrat-700',
          },
        ]}
      />
      <LineChart
        data={timeGraphData}
        width={graphWidth}
        height={220}
        withHorizontalLines={false}
        withVerticalLines={false}
        withDots
        withShadow={false}
        chartConfig={chartConfig}
        style={styles.chart}
      />
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterScrollView}
      >
        {availableFilterOptions.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[
              styles.filterTile,
              selectedFilter === filter.id && styles.filterTileSelected,
            ]}
            onPress={() => setSelectedFilter(filter.id)}
          >
            <Text
              style={[
                styles.filterText,
                selectedFilter === filter.id && styles.filterTextSelected,
              ]}
            >
              {filter.label}
              {filter.id !== PRESET_IDENTIFIERS.ALL &&
                ` (${questionCounts[filter.id] || 0})`}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

NewGameAnalysisGraph.propTypes = {
  userSubmissionTimes: PropTypes.array,
  opponentSubmissionTimes: PropTypes.array,
  questions: PropTypes.array,
};

export default React.memo(NewGameAnalysisGraph);
