import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Image, StyleSheet, View , Text} from 'react-native';
import GameHeaderPlayerCard from '@/src/modules/game/components/usercard/GameHeaderPlayerCard';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import _toNumber from 'lodash/toNumber';
import _isEmpty from 'lodash/isEmpty';
import cardBackground from '@/assets/images/gameMobBack.png';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import useGameContext from '../../../hooks/useGameContext';
import dark from '../../../../../core/constants/themes/dark';
import getCurrentTimeWithOffset from '../../../../../core/utils/getCurrentTimeWithOffset';
import useHandleGameLeaderboard from '../../../hooks/useHandleGameLeaderboard';
import styles from './NewGameAnalysisHeader.style';

const NewGameAnalysisHeader = (props) => {
  const { players, game } = useGameContext();
  const { config = EMPTY_OBJECT, startTime, gameType } = game;
  const { isMobile } = useMediaQuery();
  const { adaptedPlayers } = props;
  const { timeLimit } = config;

  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${Math.floor(remainingSeconds)}`;
  }, []);
  

  const player0Score = adaptedPlayers[0]?.score ?? 0
  const player1Score = adaptedPlayers[1]?.score ?? 0
  const renderPlayer = useCallback(
    ({ user, score }) => {
      if (_isEmpty(user)) return null;
      return <GameHeaderPlayerCard user={user} score={score} gameType={gameType} />
    },
    [ isMobile ],
  );

  
 
    return (
      <View style={[styles.container, styles.containerMob]}>
        {renderPlayer({ user: players[0], score: player0Score})}
          <View style={[styles.timerContainer]}>
            <MaterialIcons name="timer" color={dark.colors.timerColor} size={10} />
            <Text style={styles.timerText2}> {formatTime(timeLimit)}</Text>
          </View>
          
        {renderPlayer({ user: players[1], score: player1Score })}
      </View>
    );
};

export default NewGameAnalysisHeader;
