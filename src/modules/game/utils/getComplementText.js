import _isNil from "lodash/isNil"
import _isNaN from "lodash/isNaN"
import { getFormattedTimeWithMS } from "../../../core/utils/general"


export const getValueText = (value) => {
    if (_isNil(value) || _isNaN(value)) {
        return "-"
    }

    if (value > 0) {
        return `${getFormattedTimeWithMS(value)}`
    }

    return "-"
}

export const getComplementTextString = ({ currUserScores, globalBestTime, globalAvgTime }) => {
    let message = "";

    if (currUserScores.questionAvgTime < globalBestTime) {
        message = `🎉 Amazing! You've beaten the global best time for preset challenges. Keep it up! 🏆`;
    } else if (currUserScores.questionAvgTime === globalBestTime) {
        message = `🎉 Amazing! You've reached the global best time for preset challenges. Keep it up! 🏆`;
    } else if (currUserScores.questionAvgTime < globalAvgTime) {
        message = `👏 You're faster than the global average time (${getValueText(globalAvgTime)}s) in preset challenges. Keep shining! 🌟`;
    } else if (currUserScores.questionAvgTime === globalAvgTime) {
        message = `👏 You've reached the global average time (${getValueText(globalAvgTime)}s) in preset challenges. Keep shining! 🌟`;
    } else if (currUserScores.questionAvgTime < currUserScores.presetAvgTime) {
        message = `🎯 Great job! You're solving questions faster (${getValueText(currUserScores.questionAvgTime)}s) than your preset average time. Stay consistent! ✨`;
    } else if (currUserScores.questionAvgTime > globalAvgTime) {
        message = `🤔 Your average time for preset challenges (${getValueText(currUserScores.presetAvgTime)}s) is above the global average. Keep practicing to improve! 🔥`;
    } else {
        message = `🌟 Remember: Progress is personal. Celebrate every improvement and keep striving for your best! 🚀`;
    }

    return message;
}
