import React, { useCallback } from 'react'
import PropTypes from 'prop-types'
import { View, StyleSheet } from 'react-native'
import { Text } from '@rneui/themed'
import { useRouter } from 'expo-router'

import { GAME_STATUS_LABEL } from '../../constants/game'
import Dark from '@/src/core/constants/themes/dark'
import SecondaryButton from 'atoms/SecondaryButton'
import styles from './InActiveGame.style';

const InActiveGame = (props) => {
    const { game } = props

    const router = useRouter()

    const { gameStatus } = game

    const label = `Sorry, Game you are looking for has been ${GAME_STATUS_LABEL[gameStatus]}`

    const gotToHome = useCallback(() => {
        router.replace('/home')
    }, [router])

    return (
        <View style={styles.container}>
            <Text style={styles.label}>{label}</Text>
            <SecondaryButton
                label={'Create a new game'}
                onPress={gotToHome}
                buttonStyle={styles.buttonStyle}
            />
        </View>
    )
}

InActiveGame.propTypes = {
    game: PropTypes.object,
}

export default InActiveGame
