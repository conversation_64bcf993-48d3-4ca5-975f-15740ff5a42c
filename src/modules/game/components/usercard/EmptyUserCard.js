import React, { useEffect, useState } from 'react'
import { Animated, Easing, View } from 'react-native'
import _isEmpty from 'lodash/isEmpty'
import Dark from '@/src/core/constants/themes/dark'
import FontAwesome6 from '@expo/vector-icons/FontAwesome6'

const EmptyUserCard = ({ showAnimation, styles }) => {
    const shadowSize = useState(new Animated.Value(40))[0]

    useEffect(() => {
        if (showAnimation) {
            const animateShadow = Animated.loop(
                Animated.sequence([
                    Animated.timing(shadowSize, {
                        toValue: 56,
                        duration: 1000,
                        easing: Easing.linear,
                        useNativeDriver: false,
                    }),
                    Animated.timing(shadowSize, {
                        toValue: 40,
                        duration: 1000,
                        easing: Easing.linear,
                        useNativeDriver: false,
                    }),
                ]),
                {
                    iterations: -1, // Infinite loop
                }
            )
            animateShadow.start()

            return () => animateShadow.stop()
        }
    }, [showAnimation, shadowSize])

    return (
        <View style={styles.emptyCard}>
            {showAnimation && (
                <Animated.View
                    style={[
                        styles.shadowCircle,
                        {
                            width: shadowSize,
                            height: shadowSize,
                        },
                    ]}
                />
            )}
            <View style={styles.emptyUser}>
                <FontAwesome6
                    name="circle-user"
                    size={32}
                    color={Dark.colors.textDark}
                />
            </View>
        </View>
    )
}

export default EmptyUserCard
