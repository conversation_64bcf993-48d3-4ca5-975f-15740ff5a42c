import React, { useCallback } from 'react'

import { View } from 'react-native'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import { Text } from '@rneui/themed'
import _size from 'lodash/size'

import styles from './Header.style'

const Header = (props) => {
    const { timeSpent, currentScore, maxScore } = props

    const timeLeft = 60 - timeSpent

    const getFormattedTime = useCallback(() => {
        const minutes = Math.floor(timeLeft / 60)
        const remainingSeconds = timeLeft % 60
        return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
    }, [timeLeft])

    const renderScore = useCallback(() => {
        return (
            <Text style={styles.playerScore}>
                {currentScore}/{maxScore}
            </Text>
        )
    }, [currentScore, maxScore])

    return (
        <View style={styles.container}>
            <View style={styles.timerBox}>
                <MaterialIcons name={'timer'} color={'white'} size={20} />
                <Text style={styles.timerText}> {getFormattedTime()}</Text>
            </View>
            {renderScore()}
        </View>
    )
}

Header.propTypes = {}

export default React.memo(Header)
