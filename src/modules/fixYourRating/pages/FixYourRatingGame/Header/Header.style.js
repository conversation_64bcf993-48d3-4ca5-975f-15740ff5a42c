import { StyleSheet } from 'react-native'
import Dark from 'core/constants/themes/dark'

const styles = StyleSheet.create({
    container: {
        justifyContent: 'space-between',
        paddingTop: 24,
        alignItems: 'center',
        gap: 16,
    },
    timerBox: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        width: 100,
        justifyContent: 'center',
    },
    timerText: {
        fontSize: 16,
        fontFamily: 'Montserrat-700',
        color: 'white',
    },
    playerScore: {
        color: Dark.colors.secondary,
        fontSize: 22,
    },
})

export default styles
