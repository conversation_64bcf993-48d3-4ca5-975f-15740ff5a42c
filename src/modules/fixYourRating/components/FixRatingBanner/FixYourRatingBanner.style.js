import { useMemo } from "react";
import { Platform, StyleSheet } from "react-native";
import useMediaQuery from 'core/hooks/useMediaQuery'

const createStyles = (isCompactMode, isNativeDevice) => StyleSheet.create({
    gradientContainer: {
        borderRadius: isCompactMode ? 10 : 20,
        overflow: 'hidden',
    },
    container: {
        width: "100%",
        borderRadius: isCompactMode ? 10 : 20,
        overflow: 'hidden',
        paddingHorizontal: isNativeDevice ? 16 : isCompactMode ? 0 : 20,
        paddingVertical: isCompactMode ? 0 : 18,
    },
    button: {
        maxWidth: 180,
        backgroundColor: '#2881C3',
        borderRadius: 20,
        paddingHorizontal: 15,
        paddingVertical: 6,
        alignItems: 'center',
    },
    title: {
        fontSize: isCompactMode ? 12 : 18,
        fontFamily: 'Montserrat-700',
        color: '#FFF',
        marginBottom: isCompactMode ? 0 : 10,
    },
    buttonText: {
        fontSize: 12,
        lineHeight: 13,
        fontFamily: 'Montserrat-600',
        color: 'white',
    },
    description: {
        fontFamily: 'Montserrat-600',
        fontSize: 12,
        color: '#FFF',
        marginBottom: isCompactMode ? 8 : 20,
    },
})

const useFixYourRatingBannerStyles = () => {
    const { isMobile } = useMediaQuery();
    const isNativeDevice = Platform.OS !== 'web'

    const styles = useMemo(() => createStyles(isMobile, isNativeDevice), [isMobile, isNativeDevice]);

    return styles;
};

export default useFixYourRatingBannerStyles
