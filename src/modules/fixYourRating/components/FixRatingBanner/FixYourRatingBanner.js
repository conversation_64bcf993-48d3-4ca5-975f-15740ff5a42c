import { Text, View, ImageBackground, Platform, Image } from "react-native"
import { useSession } from "../../../auth/containers/AuthProvider"
import _isNil from "lodash/isNil";
import React, { useCallback, useEffect } from "react";
import LinearGradient from "../../../../components/atoms/LinearGradient";
import dark from "../../../../core/constants/themes/dark";
import FixRatingBanner from '@/assets/images/backgrounds/banner_bg.png'
import useFixYourRatingBannerStyles from "./FixYourRatingBanner.style";
import { TouchableOpacity } from "react-native";
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useRouter } from "expo-router";
import fixYourRatingIcon from 'assets/images/icons/fix_rating_icon.png'
import useMediaQuery from 'core/hooks/useMediaQuery'
import Analytics from "../../../../core/analytics";
import { ANALYTICS_EVENTS } from "../../../../core/analytics/const";
import { PAGE_NAME_KEY, PAGE_NAMES } from "../../../../core/constants/pageNames";
import CarouselBanner from "../../../../components/shared/CarouselBanner/CarouselBanner";
import RatingBanner from 'assets/images/banners/Rating.png'
import DesktopRatingBanner from 'assets/images/banners/Desktop/Rating.png'


let trackedFixYourRatingBannerVisibility = false;

const FixYourRatingBanner = React.memo(() => {
    const isNativeDevice = Platform.OS === "ios" || Platform.OS === "android"

    const styles = useFixYourRatingBannerStyles()
    const { isMobile: isCompactMode } = useMediaQuery()

    const router = useRouter()

    const onKnowMorePressed = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_DISCOVER_RATING_BANNER, {
            [PAGE_NAME_KEY]: PAGE_NAMES.ARENA_PAGE
        });
        router.push('/fix-your-rating')
    })

    useEffect(() => {
        if (!trackedFixYourRatingBannerVisibility) {
            Analytics.track(ANALYTICS_EVENTS.ARENA.VIEWED_DISCOVER_RATING_BANNER, {
                [PAGE_NAME_KEY]: PAGE_NAMES.ARENA_PAGE,
            });
            trackedFixYourRatingBannerVisibility = true
        }
    }, []);

    return (
        <CarouselBanner
            backgroundColor={'#2C4151'}
            buttonColor={'#2881C3'}
            backgroundImage={isCompactMode ? RatingBanner : DesktopRatingBanner}
            buttonText={'Know more'}
            description={'Get a rating that truly reflects your skill level and compete with players at your level.'}
            onPress={onKnowMorePressed}
            title={'DISCOVER YOUR RATING'}
        />
    )
})

const FixYourRatingBannerContainer = () => {
    const { user } = useSession()

    if (_isNil(user)) {
        return null
    }

    const { hasFixedRating = false } = user

    if (hasFixedRating) {
        return null
    }

    return <FixYourRatingBanner />
}

export default FixYourRatingBannerContainer;