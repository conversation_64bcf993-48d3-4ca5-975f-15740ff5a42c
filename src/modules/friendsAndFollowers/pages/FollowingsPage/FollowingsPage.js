import { View, Text } from "react-native"
import useMediaQuery from "core/hooks/useMediaQuery"
import useFollowingsPageStyles from "./FollowingsPage.style"
import Entypo from '@expo/vector-icons/Entypo'
import Header from "shared/Header"
import { closeRightPane } from "molecules/RightPane/RightPane"
import React, { useCallback, useEffect } from "react"
import FriendCard from "../../components/FriendCard"
import useGetFollowings from "../../hooks/queries/useGetFollowings"

import PaginatedList from "shared/PaginatedList/PaginatedList"
import FollowersAndFolloweeCard from "../../components/FollowersAndFolloweeCard"
import Analytics from "../../../../core/analytics"
import { ANALYTICS_EVENTS } from "../../../../core/analytics/const"
import { PAGE_NAMES } from "../../../../core/constants/pageNames"

const PAGE_SIZE = 50

const FollowingsPage = () => {
    const { isMobile: isCompactMode } = useMediaQuery()

    const { fetchFollowings, updateFollowingsCache, data: followingsData } = useGetFollowings({ pageSize: PAGE_SIZE })

    const styles = useFollowingsPageStyles()

    const renderFollowingsListItem = useCallback(({ item, index, onRemove }) => {
        return <FollowersAndFolloweeCard infoData={item} onRemove={onRemove} isFollowerCard={false} />
    }, [])

    const fetchFollowingsData = useCallback(async ({ pageNumber }) => {
        const response = await fetchFollowings({ pageNumber });
        const { data } = response;
        const { getFollowings: followingsObject } = data ?? EMPTY_OBJECT;
        const { results, totalResults } = followingsObject;
        return { data: results, totalItems: totalResults };
    }, [fetchFollowings]);


    useEffect(() => {
        webengage?.screen?.(PAGE_NAMES.FOLLOWINGS_PAGE);
        Analytics.track(ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.VIEWED_FOLLOWINGS_PAGE)
    }, []);

    return (
        <View style={styles.container}>
            <Header title={"Followings"} />
            {!isCompactMode && (
                <View style={styles.headerExpanded}>
                    <Text style={styles.editTextStyle}>Followings</Text>
                    <Entypo name="cross" size={24} color={'white'} onPress={closeRightPane} />
                </View>
            )}

            <View style={{ flex: 1, paddingHorizontal: 16 }}>
                <PaginatedList
                    key={JSON.stringify(followingsData)}
                    placeholderComponent={null}
                    fetchData={fetchFollowingsData}
                    renderItem={renderFollowingsListItem}
                    renderHeader={() => null}
                    pageSize={PAGE_SIZE}
                    keyExtractor={(item, index) => ` ${index}`}
                    contentContainerStyle={{}}
                    listFooterComponent={<View style={{ height: 80 }}></View>}
                    updateCacheFunction={updateFollowingsCache}
                    dataKey={"getFollowings"}
                />
            </View>
        </View>
    )
}

FollowingsPage.propTypes = {

}

export default React.memo(FollowingsPage)