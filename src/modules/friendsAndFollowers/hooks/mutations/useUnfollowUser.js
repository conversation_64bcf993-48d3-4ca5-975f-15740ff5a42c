import { gql, useMutation } from "@apollo/client";
import { useCallback } from "react";

const UNFOLLOW_USER = gql`
  mutation UnFollowFriend($unFollowUserInput: UnFollowUserInput!) {
    unFollowUser(unFollowUserInput: $unFollowUserInput)
  }
`;

const useUnFollowUser = () => {

    const [unFollowUserQuery] = useMutation(UNFOLLOW_USER);

    const unFollowUser = useCallback(async ({ followeeId }) => {
        const response = await unFollowUserQuery({
            variables: {
                unFollowUserInput: {
                    userId: followeeId
                }
            }
        })

        return response
    }, [unFollowUserQuery])

    return {
        unFollowUser
    }
};

export default useUnFollowUser