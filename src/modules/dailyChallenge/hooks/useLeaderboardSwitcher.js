import {useCallback, useMemo, useState} from "react";
import {ANALYTICS_EVENTS} from "../../../core/analytics/const";
import Analytics from "../../../core/analytics";
import {PAGE_NAME_KEY, PAGE_NAMES} from "../../../core/constants/pageNames";
import YesterdayBg from '@/assets/images/dc/yesterday.png'
import TodayBg from '@/assets/images/dc/today.png'

export const getFormattedDate = ({isToday}) => {
    const date = new Date(getCurrentTime());

    if (!isToday) {
        date.setDate(date.getDate() - 1);
    }

    const options = {weekday: 'long', month: 'short', day: 'numeric'};
    return date.toLocaleDateString("en-US", options);
};

const useLeaderboardSwitcher = () => {

    const today = new Date()
    const todayDate = new Date().toISOString();
    const yesterday = new Date(today);

    yesterday.setDate(today.getDate() - 1);
    const yesterdayDate = yesterday.toISOString();

    const [dateStr, setDateStr] = useState(todayDate);

    const dayFilterTabs = [
        {id: todayDate, label: `Today`, dayLabel: getFormattedDate({isToday: true}), icon: TodayBg},
        {id: yesterdayDate, label: `Yesterday`, dayLabel: getFormattedDate({isToday: false}), icon: YesterdayBg},
    ]

    const onDayFilterTabPress = useCallback(({tab}) => {
        Analytics.track(ANALYTICS_EVENTS.DAILY_CHALLENGE.CHANGED_DC_LEADERBOARD_DAY_TAB, {
            challengeDate: tab.id,
            day: tab.label,
            [PAGE_NAME_KEY]: PAGE_NAMES.DAILY_CHALLENGE_LEADERBOARD
        })
        setDateStr(tab.id);
    }, [setDateStr])

    const dayFilterProps = useMemo(() => ({
        tabs: dayFilterTabs,
        selectedTabId: dateStr,
        dateStr: dateStr,
        onSelectTab: onDayFilterTabPress,
    }), [dayFilterTabs, dateStr, onDayFilterTabPress])

    return {
        dateStr,
        dayFilterProps,
    }
}

export default useLeaderboardSwitcher;