import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import _reduce from 'lodash/reduce'
import _isEqual from 'lodash/isEqual'
import _isEmpty from 'lodash/isEmpty'
import _head from 'lodash/head'
import _findIndex from 'lodash/findIndex'
import _toString from 'lodash/toString'
import _map from 'lodash/map'
import useSubmitDailyChallenge from './mutations/useSubmitDailyChallenge'
import useGetDailyChallengeById from '../../../core/graphql/queries/useGetDailyChallengeById'
import { useSession } from "../../auth/containers/AuthProvider";
import useUserActivityTracker from 'core/hooks/useUserActivityTracker'
import _sum from "lodash/sum"
import ACTIVITY_TYPES from '@/src/core/constants/activityTypes'

const submittedDailyChallengeIds = {};

const useDailyChallengeQuestionsState = ({ onSubmitDailyChallengeSuccess, onSubmitDailyChallengeFailure, dailyChallengeId }) => {
    const { userId } = useSession();
    const { dailyChallenge = EMPTY_OBJECT } = useGetDailyChallengeById({ dailyChallengeId })

    const { updateActivity } = useUserActivityTracker()
    const {
        questions: initialQuestions,
        hasAttempted,
        _id: challengeId,
        challengeNumber,
        division,
    } = dailyChallenge ?? EMPTY_OBJECT

    const dailyChallengeCacheId = `${userId}_${challengeId}`;
    const dailyChallengeCacheIdRef = useRef(dailyChallengeCacheId);
    dailyChallengeCacheIdRef.current = dailyChallengeCacheId;

    const { submitDailyChallenge, loading: submittingDailyChallenge, error: dailyChallengeSubmissionError } = useSubmitDailyChallenge({ challengeId })
    const [submittedDailyChallenge, setSubmittedDailyChallenge] = useState(false)

    const initialValue = _map(initialQuestions, (questionObject) => ({
        ...questionObject,
        hasSolved: false,
        totalAttempts: 0,
    }))

    const [questions, setQuestions] = useState(initialValue)

    const questionsMap = useMemo(() => _reduce(questions, (acc, questionObj) => ({ ...acc, [questionObj?.question?.id]: questionObj }), {}), [questions]);

    const currentScore = useMemo(
        () =>
            _reduce(
                questions,
                (acc, { hasSolved }) => acc + (hasSolved ? 1 : 0),
                0
            ),
        [questions]
    )

    const updateQuestion = useCallback(
        ({ qid, value }) => {
            setQuestions(prevQuestions =>
                _map(prevQuestions, q =>
                    q.question.id === qid ? { ...q, ...value } : q
                )
            )
        },
        [setQuestions]
    )

    const [currentQuestionId, setCurrentQuestionId] = useState(
        _head(_map(questions, 'question.id'))
    )

    const [solvedAllQuestions, setSolvedAllQuestions] = useState(false)

    const updateCurrentQuestion = useCallback(() => {
        const firstUnSolvedQuestionIndex = _findIndex(questions, q => !q.hasSolved)

        if (firstUnSolvedQuestionIndex === -1) {
            setSolvedAllQuestions(true)
            return
        }

        setCurrentQuestionId(questions[firstUnSolvedQuestionIndex].question.id)
    }, [setSolvedAllQuestions, questions])

    const submitAnswer = useCallback(
        ({ questionId, value, timeTaken }) => {
            const { question: originalQuestion } = questionsMap[questionId]
            const { answers } = originalQuestion
            if (_isEqual(_toString(answers[0]), _toString(value))) {
                updateQuestion({
                    qid: questionId,
                    value: {
                        hasSolved: true,
                        timeTaken,
                    },
                })
            }
        },
        [updateQuestion, questionsMap, updateCurrentQuestion]
    )

    const totalTimeTaken = useMemo(
        () =>
            _reduce(questions, (acc, { timeTaken = 0 }) => acc + timeTaken, 0),
        [questions]
    )

    const onSubmitAllAnswers = useCallback(() => {
        const submittedTimes = _map(questions, 'timeTaken')
        if (_isEmpty(submittedTimes) || submittedDailyChallengeIds[dailyChallengeCacheId] === true) return;
        submittedDailyChallengeIds[dailyChallengeCacheId] = true;

        updateActivity({
            activityType: ACTIVITY_TYPES.DAILY_CHALLENGE,
            duration: _sum(submittedTimes)
        })

        submitDailyChallenge({ submittedTimes, challengeId, challengeNumber, division }).then(response => {
            onSubmitDailyChallengeSuccess?.();
            setSubmittedDailyChallenge(true);
        }).catch(error => {
            onSubmitDailyChallengeFailure?.();
            submittedDailyChallengeIds[dailyChallengeCacheId] = false;
        })
    }, [questions, division, challengeId, submitDailyChallenge, challengeNumber, dailyChallengeCacheId, updateActivity])

    const onSubmitAllAnswersRef = useRef(onSubmitAllAnswers)
    onSubmitAllAnswersRef.current = onSubmitAllAnswers

    const updateCurrentQuestionRef = useRef(updateCurrentQuestion)
    updateCurrentQuestionRef.current = updateCurrentQuestion

    useEffect(() => {
        updateCurrentQuestionRef.current()
    }, [questions])

    useEffect(() => {
        if (
            solvedAllQuestions &&
            submittedDailyChallengeIds[dailyChallengeCacheIdRef.current] !== true
        ) {
            onSubmitAllAnswersRef.current()
        }
    }, [solvedAllQuestions])

    if (solvedAllQuestions && submittedDailyChallengeIds[dailyChallengeCacheId] !== true) {
        onSubmitAllAnswers()
    }

    return {
        currentQuestionId,
        dailyChallenge,
        submitAnswer,
        currentScore,
        currentQuestion: questionsMap[currentQuestionId]?.question,
        solvedAllQuestions,
        totalTimeTaken,
        onSubmitAllAnswers,
        submittedDailyChallenge,
        submittingDailyChallenge,
        dailyChallengeSubmissionError
    }
}

export default useDailyChallengeQuestionsState
