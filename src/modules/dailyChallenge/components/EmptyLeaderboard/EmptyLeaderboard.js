import { useRouter } from "expo-router"
import styles from './../DailyChallengeLeaderboard/DailyChallengeLeaderboard.style'
import PropTypes from "prop-types"
import EmptyLeaderboardImage from '@/assets/images/dc/empty_dc_leaderboard.png'

import { View, Text, Image, TouchableOpacity } from "react-native"
import { useCallback } from "react"
import _isNil from "lodash/isNil"

const EmptyDCLeaderboard = (props) => {
    const { isToday, isEligible, challengeId } = props

    const router = useRouter()

    const onPressPlayNow = useCallback(() => {
        if (_isNil(challengeId)) {
            return
        }
        router.push(`/daily-challenge/${challengeId}`)
    }, [router, challengeId])

    const infoText = isEligible ? 'No mathletes has played today’s daily challenge yet,Be the first one to play' : 'No mathletes has played today’s daily challenge yet'

    if (!isToday) {
        return (
            <View style={styles.emptyLeaderboardContainer}>
                <Text style={styles.emptyLeaderboardText}>
                    Leaderboard is empty
                </Text>
            </View>
        )
    }

    return (
        <View style={styles.emptyLeaderboardContainer}>

            <Image source={EmptyLeaderboardImage} style={{ width: 96, height: 90 }} />
            <View style={{ gap: 4 }}>
                <Text style={styles.emptyLeaderboardLabel}>
                    NO MATHLETES TO SHOW
                </Text>
                <Text style={styles.emptyLeaderboardInfoText}>
                    {infoText}
                </Text>
            </View>
            {isEligible && (<TouchableOpacity onPress={onPressPlayNow}>
                <Text style={styles.takeChallengeText}>
                    Take Challenge
                </Text>
            </TouchableOpacity>)}

        </View>
    )
}

EmptyDCLeaderboard.propTypes = {
    isToday: PropTypes.bool,
    isEligible: PropTypes.bool,
    challengeId: PropTypes.any
}

export default EmptyDCLeaderboard