import { Image, Pressable, Text, View } from 'react-native';
import groupIcon from '@/assets/images/dc/group_icon.png';
import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { useRouter } from 'expo-router';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import AntDesign from '@expo/vector-icons/AntDesign';
import GradientCard from '@/src/components/molecules/GradientCard/GradientCard';
import divisionIconWhite from '@/assets/images/dc/division_icon_white.png';
import dark from 'core/constants/themes/dark';

import { DAILY_CHALLENGE_DIVISION } from 'core/constants/dailyChallenge';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Analytics from 'core/analytics/index';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import { getFormattedDate } from '../../hooks/useLeaderboardSwitcher';
import useDailyChallengeCardStyles from './CompactDailyChallengeCard.style';

const CompactDailyChallengeCard = (props) => {
  const { dailyChallenge } = props;

  const { isMobile: isCompactDevice } = useMediaQuery();

  const {
    _id: id,
    hasAttempted,
    challengeNumber,
    division,
  } = dailyChallenge ?? EMPTY_OBJECT;

  const router = useRouter();

  const isOpenDivision = division === DAILY_CHALLENGE_DIVISION.OPEN;

  const styles = useDailyChallengeCardStyles();

  const onPressed = useCallback(() => {
    const trackingAttributes = {
      [PAGE_NAME_KEY]: PAGE_NAMES.ARENA_PAGE,
      division,
      challengeId: id,
      challengeNumber,
    };
    if (hasAttempted) {
      Analytics.track(
        ANALYTICS_EVENTS.DAILY_CHALLENGE
          .CLICKED_ON_DAILY_CHALLENGE_BANNER_LIVE_LEADERBOARD,
        trackingAttributes,
      );
      router.push('/daily-challenge-leaderboard');
      return;
    }
    Analytics.track(
      ANALYTICS_EVENTS.DAILY_CHALLENGE
        .CLICKED_ON_DAILY_CHALLENGE_BANNER_PLAY_NOW,
      trackingAttributes,
    );
    router.push(`/daily-challenge/${id}`);
  }, [division, id, challengeNumber, hasAttempted, router]);

  const showAttepmtedOverlay = useCallback(() => {
    if (hasAttempted) {
      return (
        <View style={styles.overlay}>
          <AntDesign
            name="checkcircle"
            size={18}
            color={dark.colors.textDark}
          />
        </View>
      );
    }
    return null;
  }, [hasAttempted, styles.overlay]);

  return (
    <Pressable onPress={onPressed}>
      <GradientCard
        gradientColor={dark.colors.textDark}
        borderColor={dark.colors.primary}
      >
        <View style={[styles.container, isCompactDevice && { borderWidth: 0 }]}>
          <View style={{ position: 'relative' }}>
            <View style={styles.imageContainer}>
              <Image
                source={isOpenDivision ? groupIcon : divisionIconWhite}
                style={{ height: 28, width: 24 }}
                resizeMode="contain"
              />
            </View>
            {showAttepmtedOverlay()}
          </View>

          <View style={{ gap: 4, flex: 1 }}>
            <Text
              style={[styles.titleText, !isCompactDevice && { fontSize: 12 }]}
            >
              {`${division} #${challengeNumber}`}
            </Text>
            <Text style={styles.infoText} numberOfLines={2}>
              {getFormattedDate({ isToday: true })}
            </Text>
          </View>
          <FontAwesome6 name="arrow-right-long" size={16} color="white" />
        </View>
      </GradientCard>
    </Pressable>
  );
};

CompactDailyChallengeCard.propTypes = {
  dailyChallenge: PropTypes.object,
};

export default React.memo(CompactDailyChallengeCard);
