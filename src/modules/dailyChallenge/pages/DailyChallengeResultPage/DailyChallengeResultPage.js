import React, { useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView } from 'react-native';

import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import Ionicons from '@expo/vector-icons/Ionicons';
import dark from 'core/constants/themes/dark';
import Header from 'shared/Header';

import streakIcon from '@/assets/images/3dIcons/streak.png';
import staticCoinsIcon from '@/assets/images/3dIcons/staticCoins.png';
import { getFormattedTimeWithMS } from '../../../../core/utils/general';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _toNumber from 'lodash/toNumber';
import { useSession } from '../../../auth/containers/AuthProvider';
import useDailyChallengeResultPageStyles from './DailyChallengeResultPage.style';

const DEFAULT_SCORE = 60 * 60 * 1000;

const DailyChallengeResultPage = (props) => {
  const { user } = useSession();
  const styles = useDailyChallengeResultPageStyles();

  const { dailyChallenge, userResult = EMPTY_OBJECT } = props;
  const { stats: dailyChallengeStats } = dailyChallenge;
  const { result: dailyChallengeResult, stats: userDivisionStats } = userResult;

  const statikCoinsEarned = _get(dailyChallengeResult, 'statikCoinsEarned', 0);

  const userStatikCoins = _get(user, 'statikCoins', 0);

  const userTotalTime = _toNumber(
    _get(dailyChallengeResult, ['score'], DEFAULT_SCORE),
  );
  const formattedScore = getFormattedTimeWithMS(userTotalTime, true);

  const todaysAverage = _toNumber(_get(dailyChallengeStats, 'averageTime'));
  const showTodaysAverage = userTotalTime <= todaysAverage;

  const currentStreak = _get(userDivisionStats, ['streaks', 'current']);
  const highestStreak = _get(userDivisionStats, ['streaks', 'highest']);

  const challengeStatsToShow = useMemo(() => {
    const stats = [];
    if (dailyChallengeStats?.totalSubmission >= 0) {
      stats.push({
        label: 'Total Played',
        value: dailyChallengeStats?.totalSubmission,
      });
    }
    if (userDivisionStats.bestTime >= 0) {
      const formattedBestTime = getFormattedTimeWithMS(
        userDivisionStats.bestTime,
      );
      stats.push({ label: 'Your Best', value: formattedBestTime });
    }
    if (userDivisionStats.averageTime >= 0) {
      const formattedAvgTime = getFormattedTimeWithMS(
        userDivisionStats.averageTime,
      );
      stats.push({ label: 'Your Avg', value: formattedAvgTime });
    }
    return stats;
  }, [userDivisionStats, dailyChallengeStats]);

  const renderDailyChallengeStat = useCallback(
    ({ label, value }) => (
      <View style={styles.statBox}>
        <Text style={styles.statNumber}>{value}</Text>
        <Text style={styles.statLabel}>{label}</Text>
      </View>
    ),
    [],
  );

  return (
    <View style={styles.mainContainer}>
      <Header title={'Daily Challenge'} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.iconContainer}>
          <Ionicons name="rocket" size={48} color={dark.colors.secondary} />
        </View>

        {/* Title */}
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Lightning Fast!</Text>
          <Text style={styles.subtitle}>You Crushed Today's Challenge!</Text>
          <Text style={styles.division}>
            OPEN DAILY CHALLENGE #{dailyChallenge.challengeNumber}
          </Text>
        </View>

        {/* Challenge Time */}
        <View style={styles.timeContainer}>
          <View style={styles.timeText}>
            <Text style={styles.greenText}>{formattedScore}</Text>
            <Text style={styles.timeFormat}>min : sec : ms</Text>
          </View>
          {showTodaysAverage && (
            <Text style={styles.averageText}>
              Today's Avg : {getFormattedTimeWithMS(userTotalTime)}
            </Text>
          )}
        </View>

        {/* Share Button */}
        {/* <TouchableOpacity style={styles.shareButton}>
                    <Text style={styles.shareText}>Share</Text>
                    <FontAwesome5 name="share-alt" size={10} color={dark.colors.textDark} />
                </TouchableOpacity> */}

        {/* Stats Row */}
        <View style={styles.statsContainer}>
          {_map(challengeStatsToShow, renderDailyChallengeStat)}
        </View>

        {/* Footer Row */}
        <View style={styles.footerContainer}>
          <View style={styles.footerBox}>
            <Image source={streakIcon} style={{ height: 36, width: 36 }} />
            <View style={styles.footerText}>
              <Text style={styles.currentStreak}>{currentStreak}D streak</Text>
              <Text style={styles.maxStreak}>{highestStreak}D best streak</Text>
            </View>
          </View>
          <View style={styles.footerBox}>
            <Image source={staticCoinsIcon} style={{ height: 36, width: 36 }} />
            <View style={styles.footerText}>
              <Text style={styles.currentCoins}>+{statikCoinsEarned}</Text>
              <Text style={styles.maxStreak}>
                {userStatikCoins} statik coins
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default React.memo(DailyChallengeResultPage);
