import React, { useCallback, useState, useRef, useMemo } from 'react'

import { Image, ScrollView, View } from 'react-native'

import styles from './ExpandedDailyChallengeWaitingPage.style'
import { Button, Text } from '@rneui/themed'
import _map from 'lodash/map'
import PrimaryButton from 'atoms/PrimaryButton'
import useMediaQuery from 'core/hooks/useMediaQuery'
import PropTypes from 'prop-types'
import { getDCCardsData } from '../Compact/DailyChallengeCompactWaitingPage'
import InfoCardWithImage from '../../../../../components/shared/InfoCardWithImage'

const ExpandedDailyChallengeWaitingPage = (props) => {

    const { isMobile } = useMediaQuery()

    const { onPressStartChallenge, timerActive, timer, onPressLeaveChallenge, noOfQuestions, challengeNumber, division } = props

    const cardsData = useMemo(() => getDCCardsData(noOfQuestions, division), [noOfQuestions, division])

    return (
        <View style={styles.container}>
            <View style={styles.contentContainer}>
                <View style={styles.challengeTextContainer}>
                    <Text style={styles.challengeText}>
                        {`${division} #${challengeNumber}`}
                    </Text>
                </View>
                <ScrollView contentContainerStyle={styles.scrollViewStyle} showsVerticalScrollIndicator={false}
                            showsHorizontalScrollIndicator={false}>
                    {
                        _map(cardsData, (data, index) =>
                            <View style={{ minWidth: 320, maxWidth: 360 }}>
                                <InfoCardWithImage
                                    key={`${index} `}
                                    description={data.description}
                                    infoImage={data.image}
                                    title={data.title} />
                            </View>
                        )
                    }
                </ScrollView>

                {
                    timerActive && (
                        <View style={styles.timerContainer}>
                            <View style={styles.timerContentContainer}>
                                <Text style={styles.startingIn}>Starting in</Text>
                                <Text style={styles.leaveChallengeLabel}>
                                    {timer}
                                </Text>
                            </View>
                            <Button
                                title={'Leave Challenge'}
                                onPress={onPressLeaveChallenge}
                                type="clear"
                                titleStyle={styles.leaveChallengeLabel}
                            />
                        </View>
                    )
                }

                {!timerActive && (<View style={styles.buttonContainer} >
                    <PrimaryButton
                        onPress={onPressStartChallenge}
                        label="Start Challenge"
                        radius={20}
                        buttonStyle={{ height: 40, width: 180 }}
                    />
                </View>)}
                
            </View>
        </View>
    )
}

ExpandedDailyChallengeWaitingPage.propTypes = {
    onPressStartChallenge: PropTypes.func,
    timerActive: PropTypes.bool,
    timer: PropTypes.number,
    onPressLeaveChallenge: PropTypes.func,
    noOfQuestions: PropTypes.number,
    challengeNumber: PropTypes.number,
    division: PropTypes.string
}


export default React.memo(ExpandedDailyChallengeWaitingPage)
