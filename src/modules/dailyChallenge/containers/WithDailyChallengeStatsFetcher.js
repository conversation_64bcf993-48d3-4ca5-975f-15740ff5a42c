import React, { cloneElement, useRef } from "react";
import useGetDailyChallengeById from "core/graphql/queries/useGetDailyChallengeById";
import _isEmpty from "lodash/isEmpty";
import Loading from "atoms/Loading";
import ErrorView from "atoms/ErrorView";

const WithDailyChallengeStatsFetcherHOC = (props) => {
    const { dailyChallengeId, dailyChallenge: cachedDailyChallenge, children } = props;

    const { dailyChallenge, loading, error } = useGetDailyChallengeById({ dailyChallengeId });


    if (loading && _isEmpty(cachedDailyChallenge)) {
        return <Loading label={'Loading Daily Challenge'} />
    }

    if (error) {
        return <ErrorView errorMessage={"Something went wrong while fetching daily challenge"} />
    }

    return cloneElement(children, { dailyChallenge: dailyChallenge, dailyChallengeId: dailyChallengeId })

}

export default WithDailyChallengeStatsFetcherHOC;