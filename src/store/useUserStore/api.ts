import { ApolloClient, ApolloQueryResult } from "@apollo/client";
import { GET_USER_RECENT_PRESETS_QUERY, GET_USER_SAVED_PRESETS_QUERY } from "./graphql";
import { UserPresetsResponse } from "./types";

export interface UserApiInterface {
  fetchUserRecentPresets: () => Promise<ApolloQueryResult<any>>;
  fetchUserSavedPresets: () => Promise<ApolloQueryResult<any>>;
}

export class User<PERSON>pi implements UserApiInterface {
  fetchUserRecentPresets: () => Promise<ApolloQueryResult<any>> = async () => {
    const appoloClient: ApolloClient<Object> = getApolloClient();
    const resp = await appoloClient.query({
      query: GET_USER_RECENT_PRESETS_QUERY,
      fetchPolicy: 'network-only',
    });
    return resp
  }
  fetchUserSavedPresets: () => Promise<ApolloQueryResult<any>> = async () => {
    const appoloClient: ApolloClient<Object> = getApolloClient();
    const resp = await appoloClient.query({
      query: GET_USER_SAVED_PRESETS_QUERY,
      fetchPolicy: 'network-only',
    });
    return resp;
  }
}