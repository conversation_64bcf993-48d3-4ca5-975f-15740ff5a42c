import { useCallback } from 'react';
import { useRouter } from 'expo-router';
import {
  CommonActions,
  useNavigation,
  useNavigationState,
} from '@react-navigation/native';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _findIndex from 'lodash/findIndex';

const useGoBack = ({ fallback = '/home' } = EMPTY_OBJECT) => {
  const router = useRouter();
  const navigation = useNavigation();
  const rootRoutes = useNavigationState((state) => state.routes);

  const goBack = useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      router.replace(fallback);
    }
  }, [fallback, router, navigation]);

  const goBackToHome = useCallback(() => {
    const authRoutes = _find(rootRoutes, (route) => route.name === '(app)');
    if (_isEmpty(authRoutes)) {
      router.navigate('/home');
      return;
    }
    const { state } = authRoutes;
    if (_isEmpty(state)) {
      router.navigate('/home');
      return;
    }
    const { routes } = state;
    if (_isEmpty(routes)) {
      router.navigate('/home');
      return;
    }
    const homeRouteEntryIndex = _findIndex(
      routes,
      (route) => route?.name === '(tabs)' && route?.params?.screen === 'home',
    );
    if (homeRouteEntryIndex === -1) {
      router.navigate('/home');
      return;
    }

    const newRoutes = routes.slice(0, homeRouteEntryIndex + 1);

    navigation.dispatch(
      CommonActions.reset({
        index: homeRouteEntryIndex,
        routes: newRoutes,
      }),
    );
  }, [rootRoutes, router, navigation]);

  return {
    goBack,
    goBackToHome,
  };
};

export default useGoBack;
