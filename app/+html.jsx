import {ScrollViewStyleReset} from 'expo-router/html'

// This file is web-only and used to configure the root HTML for every
// web page during static rendering.

// The contents of this function only run in Node.js environments and
// do not have access to the DOM or browser APIs.
export default function Root({children}) {
    return (
        <html lang="en">
        <head>
            <meta charSet="utf-8"/>
            <meta name="apple-itunes-app" content="app-id=6738620563"/>
            <meta httpEquiv="X-UA-Compatible" content="IE=edge"/>
            <meta
                name="viewport"
                content="width=device-width, initial-scale=1, shrink-to-fit=no"
            />

            <meta name="title" content="Matiks - Gamifying Mental Aptitude"/>
            <meta name="description"
                  content="Matiks enhances mental aptitude with gamified mental math challenges. Play worldwide to improve speed and accuracy. Join now!"/>
            <meta name="keywords"
                  content="Matiks, mental aptitude, gamification, mental math, educational games, speed and accuracy, brain games"/>
            <meta name="robots" content="index, follow"/>

            <!-- Open Graph Meta Tags -->
            <meta property="og:title" content="Matiks - Gamifying Mental Aptitude"/>
            <meta property="og:description"
                  content="Matiks enhances mental aptitude with gamified mental math challenges. Play worldwide to improve speed and accuracy. Join now!"/>
            <meta property="og:url" content="https://www.matiks.in"/>
            <meta property="og:type" content="website"/>

            <!-- Twitter Card Meta Tags -->
            <meta name="twitter:card" content="summary_large_image"/>
            <meta name="twitter:title" content="Matiks - Gamifying Mental Aptitude"/>
            <meta name="twitter:description"
                  content="Matiks enhances mental aptitude with gamified mental math challenges. Play worldwide to improve speed and accuracy. Join now!"/>
            <meta name="twitter:site" content="@matiks_play"/>

            {/*
          Disable body scrolling on web. This makes ScrollView components work closer to how they do on native. 
          However, body scrolling is often nice to have for mobile web. If you want to enable it, remove this line.
        */}
            <ScrollViewStyleReset/>

            {/* Using raw CSS styles as an escape-hatch to ensure the background color never flickers in dark-mode. */}
            <style
                dangerouslySetInnerHTML={{__html: responsiveBackground}}
            />
            {/* Add any additional <head> elements that you want globally available on web... */}
        </head>
        <body>{children}</body>
        </html>
    )
}

const responsiveBackground = `
body {
  background-color: #fff;
}
@media (prefers-color-scheme: dark) {
  body {
    background-color: #000;
  }
}`
