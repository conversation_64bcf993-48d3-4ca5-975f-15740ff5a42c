import ContestMainPage from 'modules/contest/pages/Contests';
import { useEffect } from "react";
import Analytics from "core/analytics";
import { ANALYTICS_EVENTS } from '../../../../src/core/analytics/const';
import {PAGE_NAMES} from "../../../../src/core/constants/pageNames";


export default () => {
    useEffect(() => {
        webengage?.screen?.(PAGE_NAMES.CONTESTS)
        Analytics.track(ANALYTICS_EVENTS.CONTEST.VIEWED_CONTEST_NAV)
    }, []);

    return <ContestMainPage />
}