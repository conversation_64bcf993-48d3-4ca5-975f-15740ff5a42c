import MobileDrawerScreen from '@/src/modules/home/<USER>/MobileDrawerScreen'
import { useEffect } from "react";
import Analytics from "../../../src/core/analytics";
import { ANALYTICS_EVENTS } from "../../../src/core/analytics/const";
import {PAGE_NAMES} from "core/constants/pageNames";

export default () => {
    useEffect(() => {
        webengage?.screen?.(PAGE_NAMES.NAV_MORE_OPTIONS)
        Analytics.track(ANALYTICS_EVENTS.NAV.VIEWED)
    }, []);
    return <MobileDrawerScreen />
}