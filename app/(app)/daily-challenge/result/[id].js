import DailyChallengeResultPage from "modules/dailyChallenge/pages/DailyChallengeResultPage";
import { Redirect, useLocalSearchParams } from "expo-router";
import React, { useEffect } from "react";
import { PAGE_NAMES } from "core/constants/pageNames";
import _isEmpty from "lodash/isEmpty";

const DailyChallengeResultRoute = () => {
  const searchParams = useLocalSearchParams()
  const { id: dailyChallengeId } = searchParams

  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.DAILY_CHALLENGE_RESULT)
  }, []);

  if (_isEmpty(dailyChallengeId)) {
    return <Redirect href="/home" />
  }

  return <DailyChallengeResultPage dailyChallengeId={dailyChallengeId}/>
}

export default DailyChallengeResultRoute;