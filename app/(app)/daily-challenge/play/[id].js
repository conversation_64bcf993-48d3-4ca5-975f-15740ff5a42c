import React, {useEffect} from 'react';

import { Redirect, useLocalSearchParams } from "expo-router";
import _isEmpty from "lodash/isEmpty";
import DailyChallenge from 'modules/dailyChallenge/pages/DailyChallenge';
import DailyChallengeWaitingPage from '../../../../src/modules/dailyChallenge/pages/DailyChallengeWaitingPage/Expanded/ExpandedDailyChallengeWaitingPage';
import WithDailyChallengeFetcher from '../../../../src/modules/dailyChallenge/containers/WithDailyChallengeFetcher';
import {PAGE_NAMES} from "../../../../src/core/constants/pageNames";

const DailyChallengeGame = props => {
    const searchParams = useLocalSearchParams()
    const { id: dailyChallengeId } = searchParams

    useEffect(() => {
        webengage?.screen?.(PAGE_NAMES.LIVE_DAILY_CHALLENGE_PAGE)
    }, []);

    if (_isEmpty(dailyChallengeId)) {
        return <Redirect href="/home" />
    }

    return (
        <WithDailyChallengeFetcher dailyChallengeId={dailyChallengeId}>
            <DailyChallenge />
        </WithDailyChallengeFetcher>
    );
};

export default React.memo(DailyChallengeGame);