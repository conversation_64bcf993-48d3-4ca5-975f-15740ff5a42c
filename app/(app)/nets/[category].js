import { Redirect, useLocalSearchParams } from "expo-router";
import PracticeCategoryGame from "modules/practice/pages/PracticeCategoryGame";
import { usePracticeContext } from "../../_layout";

import _isEmpty from "lodash/isEmpty"

const PracticeCategory = () => {

    const { category } = useLocalSearchParams()

    const { practiceConfig, practiceSession } = usePracticeContext()

    const { selectedPresets } = practiceConfig ?? EMPTY_OBJECT

    if (_isEmpty(selectedPresets)) {
        return <Redirect href={'/home'} />
    }

    return <PracticeCategoryGame category={category} practiceConfig={practiceConfig} practiceSession={practiceSession} />
}

export default PracticeCategory