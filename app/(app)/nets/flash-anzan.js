import {useEffect} from "react";
import { Redirect, useLocalSearchParams } from "expo-router";
import FlashAnzanGame from "modules/practice/pages/FlashAnzan/FlashAnzan";
import { usePracticeContext } from "../../_layout";

import _isEmpty from "lodash/isEmpty"
import FlashAnzanQuestionsView from "modules/practice/pages/FlashAnzan/components/FlashAnzanQuestionsView";
import {PAGE_NAMES} from "core/constants/pageNames";

const FlashAnzan = () => {

    const { questionNo } = useLocalSearchParams()

    const { practiceConfig, practiceSession } = usePracticeContext()

    const { selectedConfig } = practiceConfig ?? EMPTY_OBJECT

    const { gameResults } = practiceSession ?? EMPTY_OBJECT

    useEffect(() => {
        webengage?.screen?.(PAGE_NAMES.PRACTICE_FLASH_ANZAN)
    }, []);

    if (_isEmpty(selectedConfig)) {
        return <Redirect href={'/home'} />
    }

    if (!_isEmpty(questionNo) && !_isEmpty(gameResults)) {
        return <FlashAnzanQuestionsView gameResults={gameResults} questionNo={parseInt(questionNo)} />
    }

    return <FlashAnzanGame selectedConfig={selectedConfig} />
}

export default FlashAnzan