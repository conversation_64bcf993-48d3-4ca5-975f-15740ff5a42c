import React, { useEffect } from 'react';
import FriendsPage from '../../../../../src/modules/friendsAndFollowers/pages/FriendsPage';
import { ANALYTICS_EVENTS } from '../../../../../src/core/analytics/const';
import { PAGE_NAMES } from '../../../../../src/core/constants/pageNames';
import Analytics from '../../../../../src/core/analytics';

const Friends = () => {
  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.FRIENDS_PAGE);
    Analytics.track(ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.VIEWED_FRIENDS_PAGE);
  }, []);

  return <FriendsPage />;
};

export default React.memo(Friends);
