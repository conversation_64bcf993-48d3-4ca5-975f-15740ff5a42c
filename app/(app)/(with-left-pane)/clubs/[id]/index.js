import React from 'react';
import _isEmpty from 'lodash/isEmpty';

import ClubDetailView from '@/src/modules/clubs/pages/ClubDetailView/ClubDetailView';
import { Redirect, useLocalSearchParams } from 'expo-router';

const ClubDetailedView = () => {
  const { id: clubId } = useLocalSearchParams();

  if (_isEmpty(clubId)) {
    return <Redirect href="/home" />;
  }

  return <ClubDetailView clubId={clubId} />;
};

export default React.memo(ClubDetailedView);
