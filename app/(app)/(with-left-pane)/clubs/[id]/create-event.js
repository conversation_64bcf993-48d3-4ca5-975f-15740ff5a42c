import CreateEvents from '@/src/modules/clubs/pages/CreateEvents';
import React from 'react';

import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { showRightPane } from 'molecules/RightPane/RightPane';
import { Redirect, useLocalSearchParams } from 'expo-router';

const CreateEvent = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const { id: clubId } = useLocalSearchParams();
  if (!isCompactMode) {
    showRightPane({
      content: <CreateEvents clubId={clubId} />,
    });
    return <Redirect href={`/clubs/${clubId}`} />;
  }
  return <CreateEvents clubId={clubId} />;
};

export default React.memo(CreateEvent);
