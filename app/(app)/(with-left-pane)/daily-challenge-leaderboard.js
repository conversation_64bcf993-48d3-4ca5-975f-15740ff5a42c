import React, { useEffect } from 'react'
import { View, StyleSheet } from 'react-native'
import Header from 'shared/Header'
import Analytics from "core/analytics";
import { ANALYTICS_EVENTS } from "core/analytics/const";
import DailyChallengeLeaderboardPage from '../../../src/modules/dailyChallenge/pages/DailyChallengeLeaderboardPage/DailyChallengeLeaderboardPage';
import { useLocalSearchParams } from 'expo-router';
import {PAGE_NAMES} from "../../../src/core/constants/pageNames";

const DailyChallengeLeaderboardScreen = () => {

    useEffect(() => {
        webengage?.screen?.(PAGE_NAMES.DAILY_CHALLENGE_LEADERBOARD)
        Analytics.track(ANALYTICS_EVENTS.DAILY_CHALLENGE.VISITED_DAILY_CHALLENGE_LEADERBOARD)
    }, []);

    return (
        <View style={styles.container}>
            <Header title={'Daily Challenge'} />
            <DailyChallengeLeaderboardPage/>
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
})

export default DailyChallengeLeaderboardScreen
