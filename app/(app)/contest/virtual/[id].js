import React from "react"
import { useLocalSearchParams } from "expo-router"
import { View, Text } from "react-native"
import VirtualContestPlay from "../../../../src/modules/contest/pages/VirtualContestPlay/VirtualContestPlay"

const VirtualContest = () => {
    const searchParams = useLocalSearchParams()
    const { id: contestId } = searchParams

    return <VirtualContestPlay contestId={contestId}  />
}

export default React.memo(VirtualContest)

