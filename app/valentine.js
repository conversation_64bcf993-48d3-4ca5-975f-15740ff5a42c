import React, { useCallback, useEffect, useState } from 'react';
import { Image, Platform, Text, View } from 'react-native';
import { Input } from 'tamagui';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';

import useMediaQuery from 'core/hooks/useMediaQuery';
import ValentineResult from 'modules/valentine/pages/ValentineResult';
import blindDate from '@/assets/images/blindDate.png';

import Rive from 'atoms/Rive';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { Redirect } from 'expo-router';
import Analytics from 'core/analytics';

const Valentine = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const [finding, setFinding] = useState(false);
  const [username, setUsername] = useState('');
  const [found, setFound] = useState(false);

  const onClickFindMyDate = useCallback(() => {
    if (username === '') {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: 'username Can not be empty',
      });
      return;
    }
    Analytics.track('Valentine: Clicked on Find my Date');
    setFinding(true);
    setTimeout(() => {
      setFinding(false);
      setFound(true);
    }, 5000);
  }, [username]);

  const renderUsernameInput = () => (
    <>
      <Text
        style={{
          fontFamily: 'Montserrat-700',
          fontSize: 24,
          marginBottom: 20,
          textAlign: 'center',
        }}
      >
        Find your Blind Date on Instagram
      </Text>
      <Input
        placeholder="Instagram Username"
        flex={1}
        size="$4"
        width="100%"
        textAlign="center"
        backgroundColor="white"
        value={username}
        onChangeText={setUsername}
        color="black"
      />
      <InteractivePrimaryButton
        label="Find My Date"
        buttonContainerStyle={{ width: '100%' }}
        onPress={onClickFindMyDate}
      />
    </>
  );

  const renderFindingMyDate = () => (
    <>
      <Rive
        url="https://cdn.matiks.com/files/66fc19d744b74099e82125b3_cute_heart_saying_hi.riv?timestamp=1739481950"
        autoPlay
        style={{ width: 200, height: 200, resizeMode: 'contain' }}
      />
      <Text
        style={{
          fontFamily: 'Montserrat-700',
          fontSize: 24,
          marginBottom: 20,
          textAlign: 'center',
        }}
      >
        Finding your Date...
      </Text>
    </>
  );

  useEffect(() => {
    Analytics.track('Valentine: Valentine Screen View');
  }, []);

  if (found) {
    return <ValentineResult />;
  }

  if (Platform.OS !== 'web') {
    return <Redirect href={'/home'} />;
  }

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
      }}
    >
      <Image
        source={blindDate}
        style={{ width: 200, height: 200, marginLeft: -40, marginTop: -100 }}
      />
      <View
        style={{
          maxWidth: 400,
          width: '100%',
          padding: isCompactMode ? 16 : 24,
          backgroundColor: 'white',
          borderRadius: 16,
          justifyContent: 'center',
          alignItems: 'center',
          paddingVertical: isCompactMode ? 20 : 32,
          gap: 16,
        }}
      >
        {!finding && !found && renderUsernameInput()}
        {finding && !found && renderFindingMyDate()}
      </View>
    </View>
  );
};

export default Valentine;
