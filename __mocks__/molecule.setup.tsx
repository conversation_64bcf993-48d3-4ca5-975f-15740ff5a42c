jest.mock('core/container/Haptics', () => ({
  impactAsync: jest.fn(),
  ImpactFeedbackStyle: { Light: 'Light' },
}));

jest.mock('react-native-svg', () => {
  const MockSvg = (props: any) => <>{props.children}</>;
  const MockRect = (props: any) => <>{props.children}</>;
  const MockDefs = (props: any) => <>{props.children}</>;
  const MockRadialGradient = (props: any) => <>{props.children}</>;
  const MockStop = (props: any) => <>{props.children}</>;
  return {
    __esModule: true,
    default: MockSvg,
    Svg: MockSvg,
    Rect: MockRect,
    Defs: MockDefs,
    RadialGradient: MockRadialGradient,
    Stop: MockStop,
  };
});

jest.mock('@expo/vector-icons', () => {
  const { View } = require('react-native');
  return {
    AntDesign: (props: any) => <View {...props} />,
  };
});

jest.mock('tamagui', () => {
  const RN = require('react-native');
  const React = require('react');

  const MockSheet = ({ children, open, ...props }: any) => {
    return open ? (
      <RN.View {...props} testID="mock-sheet-root">
        {children}
      </RN.View>
    ) : null;
  };
  MockSheet.Frame = ({ children, ...props }: any) => (
    <RN.View {...props} testID="mock-sheet-frame">
      {children}
    </RN.View>
  );
  MockSheet.Handle = (props: any) => (
    <RN.View {...props} testID="mock-sheet-handle" />
  );
  MockSheet.Overlay = (props: any) => (
    <RN.View {...props} testID="mock-sheet-overlay" />
  );
  MockSheet.ScrollView = ({ children, ...props }: any) => (
    <RN.ScrollView {...props} testID="mock-sheet-scrollview">
      {children}
    </RN.ScrollView>
  );

  const MockYStack = ({ children, ...props }: any) => (
    <RN.View {...props}>{children}</RN.View>
  );

  return {
    Sheet: MockSheet,
    YStack: MockYStack,
  };
});

jest.mock('@/src/core/hooks/useMediaQuery');

jest.mock('react-native/Libraries/Image/Image', () => 'Image');

jest.mock('@/src/components/shared/CardWithoutCTA/CardWithoutCTA.style.ts');

jest.mock('src/modules/auth/containers/AuthProvider', () => ({
  useSession: jest.fn(),
}));

jest.mock('src/core/utils/getUserCurrentActivity', () => ({
  getUserCurrentActivity: jest.fn(),
}));

jest.mock('src/components/shared/WithNotificationBannerAnimation', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div testID="mock-banner-animation">{children}</div>
  ),
}));

jest.mock('src/modules/game/pages/ChallengeUser/ChallengeUserStatus', () => ({
  __esModule: true,
  default: () => <div testID="mock-challenge-status">Status</div>,
}));

jest.mock('src/modules/game/pages/ChallengeUser/ChallengeRequestCard', () => ({
  __esModule: true,
  default: () => <div testID="mock-challenge-request-card">Request</div>,
}));
